<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-boot-starter</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jeecg-boot-starter-seata</artifactId>
    <description>分布式事务</description>
    <dependencies>
        <!-- seata依赖 -->
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.3.3</version>
        </dependency>

    </dependencies>
</project>