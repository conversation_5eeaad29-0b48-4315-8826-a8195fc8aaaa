spring:
  #升级SpringBoot2.6.6,允许循环依赖
  main:
    allow-circular-references: true
  profiles:
    # 当前激活环境
    active: '@profile.name@'
  cloud:
    #配置Bus id(远程推送事件)
    bus:
      id: ${spring.application.name}:${server.port}
    nacos:
      config:
        # Nacos 认证用户
        username: @config.username@
        # Nacos 认证密码
        password: @config.password@
        # 命名空间 常用场景之一是不同环境的配置的区分隔离，例如开发测试环境和生产环境的资源（如配置、服务）隔离等
        namespace: @config.namespace@
        # 配置中心地址
        server-addr: @config.server-addr@
        # 配置对应的分组
        group: @config.group@
        # 配置文件后缀
        file-extension: yaml
        prefix: @prefix.name@
        # 支持多个共享 Data Id 的配置，优先级小于extension-configs,自定义 Data Id 配置 属性是个集合，内部由 Config POJO 组成。Config 有 3 个属性，分别是 dataId, group 以及 refresh
#        shared-configs[0]:
#          data-id: @<EMAIL> #分库分表配置
#          group: @config.group@
#          refresh: false
#        shared-configs[1]:
#          data-id: @<EMAIL> # 配置文件名-Data Id
#          group: @config.group@   # 默认为DEFAULT_GROUP
#          refresh: false   # 是否动态刷新，默认为false
      discovery:
        namespace: @config.namespace@
        server-addr: @config.server-addr@
        watch:
          enabled: false
# feign启用sentinel
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        #不设置connectTimeout会导致readTimeout设置不生效
        connectTimeout: 5000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true