<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-boot-starter</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jeecg-boot-starter-rabbitmq</artifactId>
    <description>jeecg-boot-starter-消息队列</description>
    <dependencies>
        <!-- 消息总线 rabbitmq -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        </dependency>
    </dependencies>

</project>