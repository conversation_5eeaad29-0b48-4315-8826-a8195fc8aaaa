package org.jeecg.modules.camunda.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.entity.ActNode;
import org.jeecg.modules.camunda.entity.Position;
import org.jeecg.modules.camunda.entity.Role;
import org.jeecg.modules.camunda.mapper.ActNodeMapper;
import org.jeecg.modules.camunda.service.IActNodeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 流程节点扩展表
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月15日 23:26
 */
@Service
public class ActNodeServiceImpl extends ServiceImpl<ActNodeMapper, ActNode> implements IActNodeService {

    public void deleteByNodeId(String id, String procDefId) {
        this.remove(new LambdaQueryWrapper<ActNode>().eq(ActNode::getNodeId, id).eq(ActNode::getProcDefId, procDefId));
    }

    public List<LoginUser> findUserByNodeId(String nodeId, String procDefId) {
        List<LoginUser> users = this.baseMapper.findUserByNodeId(nodeId, procDefId);
        if (users.size() == 0) users = Lists.newArrayList();
        return users;
    }

    public List<Position> findPostByNodeId(String nodeId, String procDefId) {
        return this.baseMapper.findPostByNodeId(nodeId, procDefId);
    }

    public List<Role> findRoleByNodeId(String nodeId, String procDefId) {
        return this.baseMapper.findRoleByNodeId(nodeId, procDefId);
    }

    public Boolean hasChooseSponsor(String nodeId, String procDefId) {
        List<ActNode> listNode = findByNodeIdAndType(nodeId, procDefId, 3);
        if (listNode != null && listNode.size() > 0) {
            return true;
        }
        return false;
    }

    public List<ActNode> findByNodeIdAndType(String nodeId, String procDefId, int type) {
        return list(new LambdaQueryWrapper<ActNode>().eq(ActNode::getNodeId, nodeId).eq(ActNode::getProcDefId, procDefId).eq(ActNode::getType, type));
    }

    public List<ActNode> findByProcDefIdAndType(String procDefId, int type) {
        return list(new LambdaQueryWrapper<ActNode>().eq(ActNode::getProcDefId, procDefId).eq(ActNode::getType, type));
    }

    public List<String> findFormVariableByNodeId(String nodeId, String procDefId) {
        return this.baseMapper.findFormVariableByNodeId(nodeId, procDefId);
    }

    public Boolean hasChooseSign(String nodeId, String procDefId) {
        List<ActNode> listNode = findByNodeIdAndType(nodeId, procDefId, 8);
        if (listNode != null && listNode.size() > 0) {
            return true;
        }
        return false;
    }
}
