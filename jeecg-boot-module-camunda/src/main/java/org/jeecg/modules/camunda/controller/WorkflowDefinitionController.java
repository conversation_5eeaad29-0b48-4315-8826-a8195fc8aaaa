package org.jeecg.modules.camunda.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.repository.ProcessDefinitionQuery;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.camunda.entity.param.BasePageParam;
import org.jeecg.modules.camunda.service.impl.WorkflowDefinitionServiceImpl;
import org.jeecg.modules.camunda.utils.CamundaUtils;
import org.jeecg.modules.camunda.vo.ProcessDefinitionVO;
import org.jeecg.modules.camunda.vo.WorkflowNodesVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模型定义相关类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年08月15日 14:27
 */
@RestController
@RequestMapping("/camunda/workflow/definition")
@Slf4j
public class WorkflowDefinitionController {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private WorkflowDefinitionServiceImpl workflowDefinitionService;

    /**
     * 分页获取流程定义列表 hzk
     *
     * @param request
     * @return
     */
    @RequestMapping("/listData")
    @ResponseBody
    public Result modelListData(HttpServletRequest request) {
        log.info("-------------模型列表-------------");
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        Integer pageNo = request.getParameter("pageNo") != null ? Integer.parseInt(request.getParameter("pageNo")) : 1;
        Integer pageSize = request.getParameter("pageSize") != null ? Integer.parseInt(request.getParameter("pageSize")) : 10;
        BasePageParam basePageParam = new BasePageParam();
        basePageParam.setPageNo(pageNo);
        basePageParam.setPageSize(pageSize);
        String keyWord = request.getParameter("keyWord");//搜索关键字

        if (StringUtils.isNotBlank(keyWord) && StringUtils.isNotBlank(keyWord.substring(1, keyWord.length() - 1))) {
            processDefinitionQuery.processDefinitionNameLike("%" + keyWord + "%");
        }
        //计算分页参数
        Integer curPageStartIndex = CamundaUtils.getPageStartIndex(basePageParam);
        List<ProcessDefinition> list = processDefinitionQuery.latestVersion().orderByProcessDefinitionId().desc().listPage(curPageStartIndex, pageSize);
        //查询任务总数
        Long totalCount = processDefinitionQuery.count();
        List<ProcessDefinitionVO> definitionVOList = list.stream().map(processDefinition -> {
            ProcessDefinitionVO model = new ProcessDefinitionVO();
            BeanUtils.copyProperties(processDefinition, model);
            return model;
        }).collect(Collectors.toList());
        Page<ProcessDefinitionVO> page = new Page<>(pageNo, pageSize, totalCount);
        page.setRecords(definitionVOList);
        return Result.ok(page);
    }

    /**
     * 选择器获取流程定义列表 hzk
     *
     * @param request
     * @return
     */
    @RequestMapping("/listNoPageData")
    @ResponseBody
    public Result modelListNoPageData(HttpServletRequest request) {
        log.info("-------------选择器获取流程定义列表-------------");
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        String keyWord = request.getParameter("keyWord");//搜索关键字

        if (StringUtils.isNotBlank(keyWord) && StringUtils.isNotBlank(keyWord.substring(1, keyWord.length() - 1))) {
            processDefinitionQuery.processDefinitionNameLike("%" + keyWord + "%");
        }
        List<ProcessDefinition> list = processDefinitionQuery.latestVersion().orderByProcessDefinitionId().desc().list();
        List<ProcessDefinitionVO> definitionVOList = list.stream().map(processDefinition -> {
            ProcessDefinitionVO model = new ProcessDefinitionVO();
            BeanUtils.copyProperties(processDefinition, model);
            return model;
        }).collect(Collectors.toList());
        return Result.ok(definitionVOList);
    }

    /**
     * 流程图bpmn xml 字符串
     *
     * @param procDefId 流程定义id
     * @return
     */
    //    @ApiOperation("流程图bpmn xml 字符串")
    @GetMapping("/bpmnXml/{procDefId}")
    public Result<String> getDefinitionXmlString(@PathVariable String procDefId) {
        return workflowDefinitionService.getDefinitionXmlString(procDefId);
    }

    @ApiOperation("获取流程节点")
    @GetMapping("/getNodeIds/{proInsId}")
    public Result<WorkflowNodesVO> getNodeIds(@PathVariable String proInsId) {
        return Result.OK( workflowDefinitionService.getNodeIds(proInsId) );
    }
}
