在服务器的 /usr/share/fonts 目录下面新建一个目录win然后将Fonts目录里的字体文件全部拷贝进去
在需要使用字体的指定一下字体的路径，比如在aspose里面word转pdf的时候需要设置
FontSettings.getDefaultInstance().setFontsFolders(
                    new String[] {"/usr/share/fonts", "/usr/share/fonts/win", "/usr/share/fonts/commercial_free"}
                    , true);


# 成绩报告导出功能会给用户导出PDF文件，考虑到PDF文件有可能会嵌入字体文件，涉及到字体商用授权问题。
# 权衡了规避风险的收益和付出，决定使用商用免费字体，字体文件在 commercial_free 目录下面。
# 部署时，需要将这些字体文件安装到系统中，以便生成的PDF文件能够正常显示。
# Linux
将 commercial_free 里面的字体文件拷贝到 /usr/share/fonts/commercial_free 目录下面
```bash
sudo cp -pr commercial_free /usr/share/fonts/commercial_free
```
# Windows
双击字体文件，然后点击安装按钮即可