<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-cloud-module</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>3.2.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>jeecg-cloud-test</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-base-core</artifactId>
        </dependency>
    </dependencies>

    <modules>
        <module>jeecg-cloud-test-shardingsphere</module>
        <module>jeecg-cloud-test-more</module>
        <module>jeecg-cloud-test-rabbitmq</module>
        <module>jeecg-cloud-test-seata</module>
    </modules>
</project>