server:
  port: 9999
spring:
  application:
    name: jeecg-gateway
  main:
    #循环依赖默认情况下已经被禁止了
    allow-circular-references: true
    allow-bean-definition-overriding: true
  cloud:
    #Sentinel配置
    sentinel:
      transport:
        dashboard: jeecg-boot-sentinel:9000
      # 支持链路限流
      web-context-unify: false
      filter:
        enabled: false
      # 取消Sentinel控制台懒加载
      eager: false
      datasource:
        #流控规则
        flow:  # 指定数据源名称
          # 指定nacos数据源
          nacos:
            server-addr: @config.server-addr@
            # 指定配置文件
            dataId: ${spring.application.name}-flow-rules
            # 指定分组
            groupId: SENTINEL_GROUP
            # 指定配置文件规则类型
            rule-type: flow
            # 指定配置文件数据格式
            data-type: json
        #降级规则
        degrade:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-degrade-rules
            groupId: SENTINEL_GROUP
            rule-type: degrade
            data-type: json
        #系统规则
        system:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-system-rules
            groupId: SENTINEL_GROUP
            rule-type: system
            data-type: json
        #授权规则
        authority:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-authority-rules
            groupId: SENTINEL_GROUP
            rule-type: authority
            data-type: json
        #热点参数
        param-flow:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-param-rules
            groupId: SENTINEL_GROUP
            rule-type: param-flow
            data-type: json
        #网关流控规则
        gw-flow:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: gw-flow
            data-type: json
        #API流控规则
        gw-api-group:
          nacos:
            server-addr: @config.server-addr@
            dataId: ${spring.application.name}-api-rules
            groupId: SENTINEL_GROUP
            rule-type: gw-api-group
            data-type: json
    gateway:
      discovery:
        locator:
          enabled: true
      globalcors:
        cors-configurations:
          '[/**]':
            allowCredentials: true
            #springboot2.4后需用allowedOriginPatterns
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
#      httpclient:
#        connect-timeout: 1000
#        response-timeout: 5s
#      # Nacos的yml方式路由配置(默认注释掉，采用数据库加载)
#        - id: jeecg-demo
#          uri: lb://jeecg-demo
#          predicates:
#            - Path=/mock/**,/test/**,/bigscreen/template1/**,/bigscreen/template2/**
#        - id: jeecg-system
#          uri: lb://jeecg-system
#          predicates:
#            - Path=/sys/**,/eoa/**,/v1/**,/joa/**,/online/**,/bigscreen/**,/jmreport/**,/desform/**,/act/**,/plug-in/**,/generic/**
#        - id: jeecg-system-websocket
#          uri: lb:ws://jeecg-system
#          predicates:
#            - Path=/websocket/**,/eoaSocket/**,/newsWebsocket/**
#        - id: jeecg-demo-websocket
#          uri: lb:ws://jeecg-demo
#          predicates:
#            - Path=/vxeSocket/**