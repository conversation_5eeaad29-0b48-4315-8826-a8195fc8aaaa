package org.jeecg.modules.transform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.transform.entity.SyncState;

import java.util.List;

/**
 * 同步标记表(SyncState)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-25 16:09:57
 */
public interface SyncStateMapper extends BaseMapper<SyncState> {

    List<Category> syncCategory(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<Knowledge> syncKnowledge(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<Rule> syncRule(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<Questions> syncQuestions(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("startIndex") Long startIndex, @Param("pageSize") Integer pageSize);

    List<Paper> syncPaper(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<PaperQuestion> syncPaperQuestion(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<UserPaper> syncUserPaper(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<Exam> syncExam(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<ExamQuestion> syncExamQuestion(@Param("teamId") String teamId, @Param("examId") Long examId);

    List<Mmpi> syncMmpi(@Param("teamId") String teamId, @Param("examId") Long examId);

    List<Study> syncStudy(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("maxSize") Long maxSize, @Param("startIndex") Long startIndex, @Param("pageSize") Integer pageSize);

    List<StudyLog> syncStudyLog(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<SysDepart> syncSysDepart(@Param("teamId") String teamId);

    List<Team> syncTeam(@Param("teamId") String teamId);

    List<SysUser> syncSysUser(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<Trainee> syncTrainee(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<SysRole> syncSysRole(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<SysUserRole> syncSysUserRole(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<SysUserDepart> syncSysUserDepart(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<SysTraineeDepart> syncSysTraineeDepart(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<SysPosition> syncSysPosition(@Param("teamId") String teamId, @Param("depRoute") String depRoute);

    List<Phase> syncPhase(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<PhaseItem> syncPhaseItem(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<UserPhase> syncUserPhase(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<UserPhaseItem> syncUserPhaseItem(@Param("teamId") String teamId, @Param("depRoute") String depRoute, @Param("pageSize") Integer pageSize);

    List<String> getDepRouteByOrgId(@Param("teamId") String teamId);

    List<String> getSyncStateIds(@Param("tableName") String tableName, @Param("dataIds") String dataIds);

    Integer addSyncState(@Param("tableName") String tableName, @Param("teamId") String teamId, @Param("id") String id);

    Integer setSyncState(@Param("tableName") String tableName, @Param("teamId") String teamId, @Param("id") String id);

    Integer resetSyncState(@Param("tableName") String tableName, @Param("dataIds") String dataIds, @Param("teamId") String teamId);

    List<Packages> syncPackages(String teamId, String depRoute);
}

