package org.jeecg.modules.training.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Data
@TableName(value = "paper", autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "paper对象", description = "试卷库")
public class Paper {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 分类id
     */
    @Excel(name = "分类id", width = 15)
    @ApiModelProperty(value = "分类id")
    private Long categoryId;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 类型：练习卷，还是考试卷
     */
    @Excel(name = "类型：1:练习卷，2:考试卷", width = 15)
    @ApiModelProperty(value = "类型：1:练习卷，2:考试卷")
    private Integer type;
    /**
     * 是否启用
     */
    @Excel(name = "是否启用", width = 15)
    @ApiModelProperty(value = "是否启用")
    private Boolean disabled;
    /**
     * 是否显示答案,0:不显示，1：显示
     */
    @Excel(name = "是否显示答案,0:不显示，1：显示", width = 15)
    @ApiModelProperty(value = "是否显示答案,0:不显示，1：显示")
    private Integer showAnswer;
    /**
     * 时效性
     */
    @Excel(name = "时效性", width = 15)
    @ApiModelProperty(value = "时效性")
    private String timeFactor;
    /**
     * 职务偏向
     */
    @Excel(name = "职务偏向", width = 15)
    @ApiModelProperty(value = "职务偏向")
    private String postFactor;
    /**
     * 应考次数：【合格试卷的次数】
     */
    @Excel(name = "应考次数", width = 15)
    @ApiModelProperty(value = "应考次数")
    private Integer numExam;
    /**
     * 自动过期的标记
     */
    @Excel(name = "自动过期的标记", width = 15)
    @ApiModelProperty(value = "自动过期的标记")
    private Integer autoExpire;
    /**
     * 抽题方式，1:按知识点抽题，2:按卷套抽题
     */
    @Excel(name = "抽题方式，1:完全随机抽题，2:按卷套抽题，3:按知识点随机抽题，4:按知识点比例抽题", width = 15)
    @ApiModelProperty(value = "抽题方式，1:完全随机抽题，2:按卷套抽题，3:按知识点随机抽题，4:按知识点比例抽题")
    private Integer drawType;
    /**
     * 判断题数量
     */
    @Excel(name = "判断题数量", width = 15)
    @ApiModelProperty(value = "判断题数量")
    private Integer judgeCount;
    /**
     * 单选数量
     */
    @Excel(name = "单选数量", width = 15)
    @ApiModelProperty(value = "单选数量")
    private Integer radioCount;
    /**
     * 多选数量
     */
    @Excel(name = "多选数量", width = 15)
    @ApiModelProperty(value = "多选数量")
    private Integer multiCount;
    /**
     * 填写题数量
     */
    @Excel(name = "填写题数量", width = 15)
    @ApiModelProperty(value = "填写题数量")
    private Integer inputCount;

    /**
     * 评分计算规则
     */
    @Excel(name = "评分计算", width = 15)
    @ApiModelProperty(value = "评分计算")
    private Long ruleId;

    /**
     * 及格分
     */
    @Excel(name = "及格分", width = 15)
    @ApiModelProperty(value = "及格分")
    private Integer qualifyScore;

    /**
     * 是否使用题库中的题号
     */
    @Excel(name = "是否使用题库中的题号", width = 15)
    @ApiModelProperty(value = "是否使用题库中的题号")
    private Integer isQuesSort;

    /**
     * 是否按题库中的题号排序
     */
    @Excel(name = "是否按题库中的题号排序", width = 15)
    @ApiModelProperty(value = "是否按题库中的题号排序")
    private Integer isQuesOrder;

    /**
     * 考试时长限制，秒。0表示不限制
     */
    @Excel(name = "考试时长限制，秒。0表示不限制", width = 15)
    @ApiModelProperty(value = "考试时长限制，秒。0表示不限制")
    private Long limitTime;
    /**
     * 题目答题思考时长限制,单位：秒
     */
    @Excel(name = "题目答题思考时长限制,单位：秒", width = 15)
    @ApiModelProperty(value = "题目答题思考时长限制,单位：秒")
    private Long thinkDuration;
    /**
     * 不合格试卷的重试次数
     */
    @Excel(name = "不合格试卷的重试次数", width = 15)
    @ApiModelProperty(value = "不合格试卷的重试次数")
    private Integer numRetries;

    @Excel(name = "开始时间设定方式：0，不设置，1上船时间，2当时时间, 3设置时间", width = 15)
    @ApiModelProperty(value = "开始时间设定方式：0，不设置，1上船时间，2当前时间")
    private Integer startFlag;

    @Excel(name = "过期时长：秒", width = 15)
    @ApiModelProperty(value = "过期时长：秒")
    private Long expiredDuration;

    @Excel(name = "告警时长：秒", width = 15)
    @ApiModelProperty(value = "告警时长：秒")
    private Long warnDuration;

    /**
     * 分组信息
     */
    @Excel(name = "所属分组", width = 30)
    @ApiModelProperty(value = "所属分组")
    private String groupFactor;

    /**
     * 自动推送
     */
    @Excel(name = "自动推送", width = 30)
    @ApiModelProperty(value = "自动推送")
    private Integer autoPush;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态", width = 15, dicCode = "del_flag")
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
    private Integer delFlag;

    /**
     * 考试保留时长（0，不保留，大于0，保留N天）
     *
     */
    @Excel(name = "考试保留时长(天)", width = 15)
    @ApiModelProperty(value = "考试保留时长（0，不保留; 大于0，保留N天）")
    private Integer keepDuration;

    private String depRoute;

    @ApiModelProperty(value = "小程序二维码文件数组")
    @TableField(value = "qr_code_files", typeHandler = FastjsonTypeHandler.class)
    private JSONArray qrCodeFiles;

    /**
     * 关联行业id集合
     */
    private String industryIds;

    /**
     * 关联行业名称集合
     */
    @TableField(exist = false)
    private String industryNames;

    private String shareMode;

    private Long shareId;

    /**
     * 是否打乱题目的选项 0 否，1 是
     */
    private Integer randomOptions;

    /**
     * 主观题打分模式 0：逐题评分；1：统一判定；2：逐题判定
     */
    private Integer scoreType;

    /**
     * 考试成绩报告模板名称(文件夹名)
     */
    @Excel(name = "成绩报告模板", width = 15)
    @ApiModelProperty(value = "成绩报告模板")
    private String reportTemplate;

    /**
     * 是否允许回查答题 否:0 是:1
     * 默认为不允许
     */
    @ApiModelProperty(value = "是否允许回查答题")
    private Integer allowCheckAnswer;

    // 是否收费 0 免费，1 收费 默认为收费
    private Integer chargeStatus;

    // 套餐类型, 1:基础版, 2:标准版, 3:尊享版 (charge_status=1时起效)
    private Integer chargeType;

    // 单独购买此学习资料的价格(单位:元), null表示此资料不能单独购买
    private BigDecimal iteamPrice;


}
