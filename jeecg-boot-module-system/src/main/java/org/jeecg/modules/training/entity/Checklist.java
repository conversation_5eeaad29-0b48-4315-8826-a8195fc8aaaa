package org.jeecg.modules.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 许可证和检查配置主表
 * @Author: jeecg-boot
 * @Date: 2022-10-23
 * @Version: V1.0
 */
@Data
@TableName("checklist")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "checklist对象", description = "许可证和检查配置主表")
public class Checklist {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 分类id
     */
    @Excel(name = "分类id", width = 15)
    @ApiModelProperty(value = "分类id")
    private Long categoryId;
    /**
     * 是否禁用
     */
    @Excel(name = "是否禁用", width = 15)
    @ApiModelProperty(value = "是否禁用")
    private Boolean disabled;
    /**
     * 流程部署id
     */
    @Excel(name = "流程部署id", width = 15)
    @ApiModelProperty(value = "流程部署id")
    private String workflowId;
    /**
     * 上传模板路径（相对）dataRoot对应的目录:<dataRoot>/checklist/templates
     */
    @Excel(name = "上传模板路径（相对）dataRoot对应的目录:<dataRoot>/checklist/templates", width = 15)
    @ApiModelProperty(value = "上传模板路径（相对）dataRoot对应的目录:<dataRoot>/checklist/templates")
    private String templateFile;
    /**
     * 职务偏向
     */
    @Excel(name = "职务偏向", width = 15)
    @ApiModelProperty(value = "职务偏向")
    private String postFactor;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
    private Integer delFlag;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
