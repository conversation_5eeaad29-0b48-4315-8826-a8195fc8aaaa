package org.jeecg.modules.training.controller.app;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBoot401Exception;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.NumberUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.training.async.download.AsyncDownloadService;
import org.jeecg.modules.training.async.download.AsyncDownloadTask;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.*;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.training.util.ExamUtils;
import org.jeecg.modules.training.vo.StudyPage;
import org.jeecg.modules.training.vo.StudyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

/**
 * @Description: 学习表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "学习表")
@RestController
@RequestMapping("/app/study")
public class AppStudyController extends JeecgController<Study, IStudyService> {

    @Value(value = "${jeecg.path.dataRoot}")
    private String dataRoot;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private IStudyLogService studyLogService;

    @Autowired
    private IStudyLogNumService studyLogNumService;

    @Autowired
    private IStudyUserStateService studyUserStateService;

    @Autowired
    private TraineeService traineeService;

    @Resource
    private PushPhaseService userPhaseHandleService;

    @Resource
    private AsyncDownloadService asyncDownloadService;

    @Resource
    private StudyStatisticsService studyStatisticsService;

    @Autowired
    private IStudyFeedbackService studyFeedbackService;

    @Autowired
    private IPackagePriceCalculationService packagePriceCalculationService;

    @Value("${training.memberEnable:false}")
    private boolean memberEnable;

    /**
     * APP端列表查询
     *
     * @param categoryId
     * @return
     */
    @AutoLog(value = "学习表-APP端列表查询")
    @ApiOperation(value = "学习表-APP端列表查询", notes = "学习表-APP端列表查询")
    @GetMapping(value = "/findStudies")
    public Result<?> findStudies(@RequestParam(name = "categoryId", required = false) Long categoryId,
                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                 @RequestParam(name = "content", required = false) String content,
                                 @RequestParam(name = "menu", required = false) String menu) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(loginUser)) {
            // 直接抛出401异常，会被全局异常处理器处理
            throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
        }

        //判断是否下船 TODO SAAS版本，应该允许下船状态下也可以登录，中远版本则不允许，先放开，后面再想方案处理
        //if (sysUserService.getById(loginUser.getId()).getStatus() == 2) {
        //    return Result.error(505,"您已经下船");
        //}
        Trainee trainee = traineeService.getById(loginUser.getId());

        //上船时间
        String boardTime = ExamUtils.getUserBoardTimeName(trainee);

        //职务
        String post = "," + trainee.getPost() + ",";

        //组
        String groupInfo = trainee.getGroupInfo();
        if (StringUtils.isNotEmpty(groupInfo)) {
            groupInfo = "," + groupInfo + ",";
        }

        StudyPage<StudyVO> studyPage = new StudyPage<>(pageNo, pageSize);

        // 组装查询条件
        LambdaQueryWrapper<StudyVO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudyVO::getDepRoute, loginUser.getDepRoute());

        // 获取按学习资料内容的Elasticsearch搜索id
        List<String> studyIdList;
        if (StringUtils.isNotEmpty(content)) {
            studyIdList = studyService.fullTextSearch(content);
            if (studyIdList != null && !studyIdList.isEmpty()) {
                lambdaQueryWrapper.and(wrapper -> wrapper.in(StudyVO::getId, studyIdList).or().in(StudyVO::getShareId, studyIdList));

                // 获取分词结果
                studyPage.setTokens(studyService.getSearchTokens(content));
            } else {
                return Result.OK(new StudyPage<>(pageNo, pageSize));
            }
        }

        IPage<StudyVO> pageList = studyService.findStudies(studyPage, categoryId, loginUser.getId(), boardTime, post, groupInfo, menu, lambdaQueryWrapper);

        return Result.OK(pageList);

    }

    /**
     * 更新记录开始时间
     *
     * @param studyId
     * @return
     */
    @AutoLog(value = "学习日志表-更新记录开始时间")
    @ApiOperation(value = "学习日志表-更新记录开始时间", notes = "学习日志表-更新记录开始时间")
    @RequestMapping(value = "/beginStudy", method = RequestMethod.GET)
    public Result<?> beginStudy(@RequestParam(name = "studyId") Long studyId) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }


        StudyLog studyLog = new StudyLog();

        studyLog.setDepRoute(loginUser.getDepRoute());
        studyLog.setStudyId(studyId);
        studyLog.setStartTime(new Date());
        studyLog.setType(0);

        StudyLog info = studyLogService.saveInfo(studyLog);
        return Result.OK("操作成功!", info.getId());
    }

    /**
     * 更新记录结束时间
     *
     * @param studyLogId
     * @return
     */
    @AutoLog(value = "学习日志表-更新记录结束时间")
    @ApiOperation(value = "学习日志表-更新记录结束时间", notes = "学习日志表-更新记录结束时间")
    @RequestMapping(value = "/finishStudy", method = RequestMethod.GET)
    public Result<?> finishStudy(@RequestParam(name = "studyLogId") Long studyLogId, @RequestParam(name = "end", required = false) Long end) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        //TODO
        //if (sysUserService.getById(loginUser.getId()).getStatus() == 2) {
        //    return Result.error(505,"您已经下船");
        //}
        StudyLog studyLog = studyLogService.getById(studyLogId);
        if (studyLog == null) {
            return Result.OK("ID不存在!");
        }

        LambdaUpdateWrapper<StudyStatistics> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.setSql("cloud = cloud + 1");
        updateWrapper.eq(StudyStatistics::getType, "study");
        studyStatisticsService.update(updateWrapper);

        // 设置该条记录来自岸基
        studyLog.setSubmitFromType(0);

        studyLog.setEndTime(new Date());
        long startTime = studyLog.getStartTime().getTime();
        long endTime = studyLog.getEndTime().getTime();
        Study study = studyService.getById(studyLog.getStudyId());
        if (study != null) {
            //类型：0：pdf；1：video；2：html

            int len = (int) ((endTime - startTime) / 1000) + 3;
            if (study.getTotalTime() == null || study.getTotalTime() == 0 || study.getTotalTime() <= len) {
                //学习耗时
                studyLog.setUseTime((long) len);
                studyLogService.saveOrUpdate(studyLog);
                studyLogNumService.saveLogNum(loginUser.getId(), studyLog.getStudyId());
                studyUserStateService.saveStatue(loginUser.getId(), studyLog.getStudyId(), 1);
                Trainee trainee = traineeService.getById(loginUser.getId());
                userPhaseHandleService.checkTrainee(trainee, 2, studyLog.getStudyId());
            } else {
                studyLogService.removeById(studyLogId);
            }
            return Result.OK("操作成功!");
        } else {
            return Result.error("此学习资料已被删除，无法提交学习结果！");
        }
    }


    /**
     * 更新考卷id
     *
     * @param studyLogId
     * @param examId
     * @return
     */
    @AutoLog(value = "学习日志表-更新考卷id")
    @ApiOperation(value = "学习日志表-更新考卷id", notes = "学习日志表-更新考卷id")
    @RequestMapping(value = "/linkStudyExam", method = RequestMethod.GET)
    public Result<?> linkStudyExam(@RequestParam(name = "studyLogId") Long studyLogId,
                                   @RequestParam(name = "examId") Long examId) {
        StudyLog studyLog = studyLogService.getById(studyLogId);
        studyLog.setExamId(examId);
        studyLogService.updateById(studyLog);
        return Result.OK("操作成功!");
    }

    /**
     * 更新关注日志
     *
     * @param studyId
     * @return
     */
    @AutoLog(value = "学习日志表-更新关注日志")
    @ApiOperation(value = "学习日志表-更新关注日志", notes = "学习日志表-更新关注日志")
    @RequestMapping(value = "/attentionStudy", method = RequestMethod.GET)
    public Result<?> attentionStudy(@RequestParam(name = "studyId") Long studyId, @RequestParam(name = "state") Integer state) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        //删除旧状态 state = 2 | 3
        LambdaQueryWrapper<StudyUserState> q = new LambdaQueryWrapper<>();
        q.eq(StudyUserState::getUserId, loginUser.getId());
        q.eq(StudyUserState::getStudyId, loginUser.getId());
        q.notIn(StudyUserState::getState, 1, state);
        studyUserStateService.remove(q);

        //保存新状态
        if (studyUserStateService.saveStatue(loginUser.getId(), studyId, state)) {
            StudyLog studyLog = new StudyLog();
            studyLog.setStudyId(studyId);
            if (state == 2) {
                studyLog.setType(1);
            } else {
                studyLog.setType(2);
            }
            Trainee user = traineeService.getById(loginUser.getId());
            studyLog.setStartTime(new Date());
            studyLog.setEndTime(new Date());
            studyLog.setDepartment(user.getTeamId());
            studyLogService.saveInfo(studyLog);
        }

        Trainee trainee = traineeService.getById(loginUser.getId());
        userPhaseHandleService.checkTrainee(trainee, 2, studyId);

        return Result.OK("操作成功!");
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学习表-通过id查询")
    @ApiOperation(value = "学习表-通过id查询", notes = "学习表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Study study = studyService.getById(id);
        return Result.OK(study);
    }

    /**
     * 根据id 获取学习文件
     *
     * @param request
     * @param response
     */
    @AutoLog(value = "学习表-据id 获取学习文件")
    @ApiOperation("学习表-据id 获取学习文件")
    @GetMapping(value = "/getStudyFile")
    @PostMapping(value = "/getStudyFile")
    public DeferredResult<Object> getStudyFile(HttpServletRequest request, HttpServletResponse response) {
        String path = "";
        try {
            String id = request.getParameter("id");
            Study study = studyService.getById(id);
            if (study == null) {
                throw new JeecgBootException("学习资料不存在");
            }
            path = dataRoot + File.separator + study.getContent();
            if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_PDF)) {
                response.setContentType("application/pdf");
            } else if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_VIDEO)) {
                response.setContentType("video/mp4");
//                request.setAttribute(NonStaticResourceHttpRequestHandler.ATTR_FILE, new File(path).toPath());
//                nonStaticResourceHttpRequestHandler.handleRequest(request, response);
//                return;
            } else if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_HTML)) {
                response.setContentType("text/html;charset=UTF-8");
            } else if (NumberUtil.equalsValue(study.getType(), CommonConstant.STUDY_TYPE_HST)) {
                response.setContentType("application/pdf");
            }

            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (oConvertUtils.isEmpty(loginUser)) {
                // 直接抛出401异常，会被全局异常处理器处理
                throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
            }

            String traineeId = loginUser.getId();
            String sysDepartId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);

            // 如果启用了会员机制，返回数据前需要先校验是否需要升级套餐或额外付费
            if (memberEnable && !packagePriceCalculationService.checkAccessPermission(traineeId, sysDepartId, (short) 2, Long.valueOf(id))) {
                DeferredResult<Object> result = new DeferredResult<>();
                result.setResult(Result.error(402, "当前套餐包含此学习资料，请升级套餐"));
                return result;
            }

            Trainee trainee = traineeService.getById(traineeId);
            if (trainee == null) {
                throw new Exception("获取失败！找不到指定船员");
            }

            AsyncDownloadTask task = asyncDownloadService.add(trainee, request, response, new File(path), study.getName());

            return task.deferredResult;

        } catch (JeecgBoot401Exception e) {
            // 捕获401异常，直接抛出，会被全局异常处理器处理
            throw e;

        } catch (Exception e) {
            log.error("获取学习文件失败:" + path, e);
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(404);
            return null;
        }
    }

    /**
     * 根据id 获取学习文件
     *
     * @param request
     * @param response
     */
    @AutoLog(value = "学习表-据id 获取学习文件")
    @ApiOperation("学习表-据id 获取学习文件")
    @GetMapping(value = "/downloadStudyFile")
    @PostMapping(value = "/downloadStudyFile")
    public void downloadStudyFile(HttpServletRequest request, HttpServletResponse response) {

        FileInputStream fis = null;
        OutputStream outputStream = null;

        try {
            String id = request.getParameter("id");
            Study study = studyService.getById(id);
            if (study == null) {
                throw new JeecgBootException("学习资料不存在");
            }

            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (oConvertUtils.isEmpty(loginUser)) {
                // 直接抛出401异常，会被全局异常处理器处理
                throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
            }

            String traineeId = loginUser.getId();
            String sysDepartId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);

            // 如果启用了会员机制，返回数据前需要先校验是否需要升级套餐或额外付费
            if (memberEnable && !packagePriceCalculationService.checkAccessPermission(traineeId, sysDepartId, (short) 2, Long.valueOf(id))) {
                response.setStatus(402);
                response.setContentType("application/json;charset=UTF-8");
                try {
                    response.getWriter().write(new com.alibaba.fastjson.JSONObject()
                            .fluentPut("success", false)
                            .fluentPut("code", 402)
                            .fluentPut("message", "当前套餐不包含此学习资料，请升级套餐")
                            .fluentPut("timestamp", System.currentTimeMillis())
                            .toString());
                } catch (IOException e) {
                    log.error("写入响应失败", e);
                }
                return;
            }

            String path = dataRoot + File.separator + study.getContent();
            fis = new FileInputStream(path);
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(study.getName().getBytes("UTF-8"), "iso-8859-1"));
            response.setContentLengthLong(new File(path).length());
            outputStream = response.getOutputStream();
            int len;
            byte[] buf = new byte[4096];
            while ((len = fis.read(buf)) != -1) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (Exception e) {
            log.error("获取学习文件失败", e.getMessage());
            response.setStatus(404);
            //e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }

            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    // 提交学习资料反馈
    @PostMapping("/submitStudyFeedback")
    public Result<?> add(@RequestBody StudyFeedback studyFeedback) {
        studyFeedbackService.submitStudyFeedback(studyFeedback);
        return Result.OK("反馈成功！请等待相关人员处理！");
    }

    /**
     * 学习资料全文搜索
     */
    @GetMapping("/fullTextSearch")
    public Result<?> fullTextSearch(@RequestParam("content") String content) {
        return Result.OK(studyService.fullTextSearch(content));
    }

    /**
     * 获取学习资料购买信息
     * 包括升级套餐价格和单独购买学习资料价格两部分信息
     *
     * @param id 学习资料ID
     * @return 学习资料购买信息
     */
    @ApiOperation(value = "获取学习资料购买信息")
    @GetMapping("/getPaymentInfo")
    public Result<?> getPaymentInfo(@RequestParam(name = "id") Long id) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (oConvertUtils.isEmpty(loginUser)) {
                throw new JeecgBoot401Exception(CommonConstant.TOKEN_IS_INVALID_MSG);
            }

            // 获取船员ID和部门ID
            String traineeId = loginUser.getId();
            String departId = StrUtil.subAfter(loginUser.getDepRoute(), "/", true);

            // 调用通用方法获取学习资料购买信息
            return Result.OK(packagePriceCalculationService.getIteamPaymentInfo(traineeId, departId, (short) 3, id));
        } catch (JeecgBoot401Exception e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学习资料购买信息失败", e);
            return Result.error("获取学习资料购买信息失败：" + e.getMessage());
        }
    }
}
