package org.jeecg.modules.training.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.Phase;
import org.jeecg.modules.training.entity.UserPhase;
import org.jeecg.modules.training.entity.UserPhaseItem;
import org.jeecg.modules.training.service.IPhaseService;
import org.jeecg.modules.training.service.IUserPhaseItemService;
import org.jeecg.modules.training.service.IUserPhaseService;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

/**
 * @Description: 用户成长阶段
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "船员培训阶段")
@RestController
@RequestMapping("/adm/userPhase")
public class UserPhaseController extends JeecgController<UserPhase, IUserPhaseService> {

    @Autowired
    private IPhaseService phaseService;

    @Autowired
    private IUserPhaseItemService userPhaseItemService;

    @Autowired
    private ISyncStateService syncStateService;

    @Autowired
    private PushPhaseService userPhaseHandleService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/getUserPhases")
    public Result<?> getUserPhases(@RequestParam("userId") String userId) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<UserPhase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.likeRight("dep_route", loginUser.getDepRoute());
        List<UserPhase> list = service.list(queryWrapper);

        list.forEach(p -> {
            Phase phase = phaseService.getById(p.getPhaseId());
            p.setName(phase.getName());
            p.setAutoPush(phase.getAutoPush());
        });

        list.sort(Comparator.comparingInt(UserPhase::getLevel));

        return Result.OK(list);
    }

    /**
     * 列表查询
     *
     * @return
     */
   /* @GetMapping(value = "/getMyPhases")
    public Result<?> getMyPhases() {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        SysUser sysUser = sysUserService.getById(loginUser.getId());

        //上船时间
        String boardTime = ExamUtils.getUserBoardTimeName(sysUser);
        //职务
        String post = "," + sysUser.getPost() + ",";

        QueryWrapper<UserPhase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", loginUser.getId());

        List<UserPhase> list = service.list(queryWrapper);

        list.forEach(p -> {
            Phase phase = phaseService.getById(p.getPhaseId());
            p.setId(p.getPhaseId());
            p.setPhaseId(null);
            p.setName(phase.getName());
            p.setAutoPush(phase.getAutoPush());
            p.setTodo(userPhaseItemService.getUserTodoCount(loginUser.getId(), phase.getLevel(), boardTime, post));
        });

        list.sort(Comparator.comparingInt(UserPhase::getLevel));

        return Result.OK(list);
    }*/


    /**
     * 添加
     *
     * @param userPhase
     * @return
     */
    @AutoLog(value = "船员培训阶段-添加")
    @ApiOperation(value = "船员培训阶段-添加", notes = "船员培训阶段-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody UserPhase userPhase) {
        LambdaQueryWrapper<UserPhase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPhase::getUserId, userPhase.getUserId())
                .eq(UserPhase::getPhaseId, userPhase.getPhaseId());
        if (!service.list(queryWrapper).isEmpty()) {
            return Result.error("此用户已经拥有此阶段。");
        }
        service.save(userPhase);

        userPhaseHandleService.pushAll();

        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param userPhase
     * @return
     */
    @AutoLog(value = "船员培训阶段-编辑")
    @ApiOperation(value = "船员培训阶段-编辑", notes = "船员培训阶段-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody UserPhase userPhase) {

        LambdaQueryWrapper<UserPhase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(UserPhase::getId, userPhase.getId())
                .eq(UserPhase::getUserId, userPhase.getUserId())
                .eq(UserPhase::getPhaseId, userPhase.getPhaseId());
        if (!service.list(queryWrapper).isEmpty()) {
            return Result.error("此用户已经拥有此阶段。");
        }

        service.updateById(userPhase);

        syncStateService.resetSyncState(CommonConstant.USER_PHASE, userPhase.getId().toString());

        //不需要
//        userPhaseHandleService.pushAll();

        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "船员培训阶段-通过id删除")
    @ApiOperation(value = "船员培训阶段-通过id删除", notes = "船员培训阶段-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {

        UserPhase userPhase = service.getById(id);
        service.removeById(id);

        LambdaQueryWrapper<UserPhaseItem> q3 = new LambdaQueryWrapper<>();
        q3.eq(UserPhaseItem::getPhaseId, userPhase.getPhaseId());
        List<UserPhaseItem> userPhaseItemList = userPhaseItemService.list(q3);
        for (UserPhaseItem item : userPhaseItemList) {
            userPhaseItemService.removeById(item.getId());
            syncStateService.resetSyncState(CommonConstant.USER_PHASE_ITEM, item.getId().toString());
        }
        syncStateService.resetSyncState(CommonConstant.USER_PHASE, id);

//        userPhaseHandleService.pushAll(true);

        return Result.ok();
    }

    /**
     * 列表查询
     *
     * @return
     */
    @AutoLog(value = "船员培训阶段-设置船员学习阶段")
    @PostMapping(value = "/setUserPhase")
    public Result<?> setUserPhase(@RequestParam("userPhaseId") Long userPhaseId) {
        service.setCurrentUserPhase(service.getById(userPhaseId));
        return Result.ok();
    }


    /**
     * 列表查询
     *
     * @return
     */
    @AutoLog(value = "船员培训阶段-批量设置船员学习阶段")
    @PostMapping(value = "/switchUsersPhase")
    public Result<?> switchUsersPhase(@RequestParam("userIds") String userIds, @RequestParam("phaseId") String phaseId) {
        String[] users = userIds.split(",");
        if (users.length > 0) {
            LambdaQueryWrapper<UserPhase> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(UserPhase::getUserId, users)
                    .eq(UserPhase::getPhaseId, phaseId);
            List<UserPhase> list = service.list(queryWrapper);

            for (UserPhase userPhase : list) {
                service.setCurrentUserPhase(userPhase);
            }
        }
        return Result.ok();
    }


    /**
     * 列表查询
     *
     * @return
     */
    @AutoLog(value = "船员培训阶段-启用禁用")
    @PostMapping(value = "/enable")
    public Result<?> enable(@RequestParam("userPhaseId") String userPhaseId, @RequestParam("enable") Boolean enable) {
        LambdaUpdateWrapper<UserPhase> up = new LambdaUpdateWrapper<>();
        up.set(UserPhase::getStatus, enable ? 0 : -1)
                .eq(UserPhase::getId, userPhaseId);
        service.update(up);

        syncStateService.resetSyncState(CommonConstant.USER_PHASE, userPhaseId);

        return Result.ok();
    }


    /**
     * 重新考试,批量
     *
     * @return
     */
    @AutoLog(value = "船员培训阶段-更新计算阶段")
    @ApiOperation(value = "船员培训阶段-更新计算阶段", notes = "船员培训阶段-更新计算阶段")
    @PostMapping(value = "/pushAllPhase")
    public Result<?> pushAllPhase() {
        if (userPhaseHandleService.pushAll(true))
            return Result.ok("执行成功");
        return Result.error("已经执行了计算，请间隔1分钟后再试。");
    }
}
