package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.training.entity.StudyUserState;
import org.jeecg.modules.training.mapper.StudyUserStateMapper;
import org.jeecg.modules.training.service.IStudyUserStateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Description: 学习日志表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Service
public class StudyUserStateServiceImpl extends ServiceImpl<StudyUserStateMapper, StudyUserState> implements IStudyUserStateService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveStatue(String userId, Long studyId, Integer state) {

        if (baseMapper.hasState(userId, studyId, state) == 0) {
            StudyUserState studyUserState = new StudyUserState();
            studyUserState.setCreateTime(new Date());
            studyUserState.setState(state);
            studyUserState.setStudyId(studyId);
            studyUserState.setUserId(userId);
            baseMapper.insert(studyUserState);
            return true;
        }

        return false;

    }

    @Override
    public boolean hasStatue(String userId, Long studyId, Integer state) {
        return baseMapper.hasState(userId, studyId, state) > 0;
    }

    @Override
    public StudyUserState getUserAttentionState(String userId, Long studyId) {
        return baseMapper.getUserAttentionState(userId, studyId);
    }

    @Override
    public StudyUserState getUserReadState(String userId, Long studyId) {
        return baseMapper.getUserReadState(userId, studyId);
    }
}
