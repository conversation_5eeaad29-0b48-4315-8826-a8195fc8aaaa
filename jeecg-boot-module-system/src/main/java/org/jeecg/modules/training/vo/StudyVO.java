package org.jeecg.modules.training.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.jeecg.modules.training.entity.Study;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * 学习视图模型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年09月05日 23:55
 */
@Data
public class StudyVO extends Study {
    /**
     * 分类名称
     */
    @Excel(name = "分类", width = 40)
    private String categoryName;

    /**
     * 考卷名称
     */
    @Excel(name = "试卷", width = 30)
    private String paperName;

    /**
     * 是否管理后台请求（1为是）
     */
    private Integer flag;

    /**
     * 阅读次数
     */
    @Excel(name = "阅读次数", width = 20)
    private Integer doneTimes;

    /**
     * 是否已阅读
     */
    @Excel(name = "阅读")
    private Boolean isRead;

    /**
     * 是否已关注
     */
    @Excel(name = "关注")
    private Integer isAttention;


    @TableField(exist = false)
    private Boolean reCalc;

    /**
     * 显示顺序（来自iteams_display_order表）
     */
    @TableField(exist = false)
    private Integer displayOrder;

}
