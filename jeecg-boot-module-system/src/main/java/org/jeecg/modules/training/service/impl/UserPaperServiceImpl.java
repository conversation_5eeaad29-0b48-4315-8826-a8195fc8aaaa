package org.jeecg.modules.training.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.NumberUtil;
import org.jeecg.modules.training.async.phase.PushPhaseService;
import org.jeecg.modules.training.entity.Exam;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.mapper.ExamMapper;
import org.jeecg.modules.training.mapper.PaperMapper;
import org.jeecg.modules.training.mapper.TraineeMapper;
import org.jeecg.modules.training.mapper.UserPaperMapper;
import org.jeecg.modules.training.service.IUserPaperService;
import org.jeecg.modules.training.util.DateTimeUtils;
import org.jeecg.modules.training.util.ExamUtils;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Service
public class UserPaperServiceImpl extends ServiceImpl<UserPaperMapper, UserPaper> implements IUserPaperService {

    final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired(required = false)
    private PaperMapper paperMapper;

    @Autowired(required = false)
    private TraineeMapper traineeMapper;

    @Autowired(required = false)
    private ExamMapper examMapper;

    @Resource
    private ISyncStateService syncStateService;

    @Override
    public List<PaperVO> findPapers(String userId, Long categoryId) {
        return baseMapper.findPapers(userId, categoryId);
    }

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    @Override
    public Result<UserPaper> deleteById(String id) {
        Result<UserPaper> result = new Result<>();
        UserPaper userPaper = this.getById(id);
        if (userPaper == null) {
            result.error500("未找到对应实体");
        } else {
            userPaper.setDelFlag(CommonConstant.DEL_FLAG_1);
            boolean ok = this.removeById(id);
            if (ok) {
                result.success("删除成功!");
            }
        }
        return result;
    }

    /**
     * 逻辑批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public Result<UserPaper> deleteBatch(List<String> ids) {
        List<UserPaper> list = new ArrayList<>();
        for (String id : ids) {
            UserPaper userPaper = this.getById(id);
            userPaper.setDelFlag(CommonConstant.DEL_FLAG_1);
            list.add(userPaper);
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.updateBatchById(list);
        }
        return Result.OK("批量删除成功！");
    }

    @Override
    public Result<UserPaper> updateStatusBatch(List<String> ids, Integer status) {
        List<UserPaper> list = new ArrayList<>();
        for (String id : ids) {
            UserPaper userPaper = this.getById(id);
            userPaper.setStatus(status);
            syncStateService.resetSyncState(CommonConstant.USER_PAPER, userPaper.getId().toString());
            list.add(userPaper);
        }
        if (!CollectionUtils.isEmpty(list)) {
            this.updateBatchById(list);
        }
        return Result.OK("批量更新成功！");
    }

    @Override
    public void processWarnStatus(String now) {
        baseMapper.processWarnStatus(now);
    }

    @Override
    public void processExpiredStatus(String now) {
        baseMapper.processExpiredStatus(now);
    }

    @Override
    public List<UserPaper> findNotPushCombination() {
        return baseMapper.findNotPushCombination();
    }

    @Override
    public List<UserPaper> findUserPush(List<String> userIds) {
        return baseMapper.findUserPush("('" + String.join("','", userIds) + "')");
    }

    @Override
    public List<UserPaper> findPaperPush(Long paperId) {
        return baseMapper.findPaperPush(paperId);
    }

    @Override
    public IPage<UserPaperVO> listPages(Page<UserPaperVO> page, QueryWrapper<UserPaperVO> queryWrapper) {
        return baseMapper.listPages(page, queryWrapper);
    }

    @Override
    @Transactional
    public void add(UserPaperVO vo) {
        if (vo == null) {
            throw new JeecgBootException("参数不能为空");
        }

        if (StringUtils.isEmpty(vo.getUserIds()) || NumberUtil.isEmpty(vo.getPaperId())) {
            throw new JeecgBootException("参数不合法！");
        }

        Paper paper = paperMapper.selectById(vo.getPaperId());
        if (paper == null) {
            throw new JeecgBootException("此试卷已经不存在，请重新选择！");
        }

        vo.setType(1);
        vo.setCreateTime(new Date());
        vo.setStatus(0);
        vo.setDepRoute(paper.getDepRoute());

        String userIds = vo.getUserIds();
        for (String userId : userIds.split(",")) {

            Trainee trainee = traineeMapper.selectById(userId);
            if (trainee == null) {
                throw new JeecgBootException("所选择的用户" + userId + "已经不存在，请重新选择！");
            }

            if (trainee.getOnboardDate() == null) {
                throw new JeecgBootException("所选择的用户" + trainee.getRealname() + "的上船时间为空，请联系管理员配置！");
            }

            vo.setUserId(userId);
            vo.setTeamId(trainee.getTeamId());
            vo.setPost(trainee.getPost());

            setupTime(vo);

            Integer boardTime = ExamUtils.getUserBoardTime(trainee, DateTimeUtils.getCurrentDate());
            if (StringUtils.isNotEmpty(paper.getTimeFactor()))
                vo.setBoardTime(boardTime);
            else
                vo.setBoardTime(null);

            List<UserPaper> list = new ArrayList<>();
            //找出现在已经有的
            if (vo.getForcePush() == null || vo.getForcePush() == 0) {
                LambdaQueryWrapper<UserPaper> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(UserPaper::getUserId, vo.getUserId())
                        .eq(UserPaper::getPaperId, vo.getPaperId())
                        .in(UserPaper::getStatus, 0, 1, 4)
                        .eq(UserPaper::getDelFlag, CommonConstant.DEL_FLAG_0);

                if (StringUtils.isNotEmpty(paper.getTimeFactor()))
                    queryWrapper.eq(UserPaper::getBoardTime, boardTime);
                else
                    queryWrapper.isNull(UserPaper::getBoardTime);
                list = list(queryWrapper);
            }

            //不存在才推送
            if (list.isEmpty()) {
                doSave(vo);
            } else {
                //重新打开停止的考试
                for (UserPaper item : list) {
                    if (item.getStatus() == 4) {
                        LambdaUpdateWrapper<UserPaper> lambdaUpdate = new LambdaUpdateWrapper<>();
                        lambdaUpdate.set(UserPaper::getStatus, 0).eq(UserPaper::getId, item.getId());
                        update(lambdaUpdate);
                    }
                }
            }
        }

    }


    private void setupTime(UserPaperVO vo) {


        if (StringUtils.isEmpty(vo.getStartTimeVal())) {
            vo.setStartTimeVal("0");
        }

        switch (vo.getStartTimeVal()) {
            case "0":
                vo.setStartTime(null);
                break;
            case "1":
                Trainee user = traineeMapper.selectById(vo.getUserId());
                if (user != null && user.getOnboardDate() != null)
                    vo.setStartTime(user.getOnboardDate());
                else
                    vo.setStartTime(DateTimeUtils.getCurrentDate());
                break;
            case "2":
                vo.setStartTime(DateTimeUtils.getCurrentDate());
                break;
            default:
                try {
                    String st = vo.getStartTimeVal();
                    st = st.replaceAll("/", "-").replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "");
                    st = st.split(" ")[0];
                    vo.setStartTime(dateFormat.parse(st));
                } catch (ParseException e) {
                    e.printStackTrace();
                    vo.setStartTime(DateTimeUtils.getCurrentDate());
                }
                break;
        }

        if (vo.getStartTime() != null) {
            if (vo.getEndTimeVal() != null && !vo.getEndTimeVal().equals("0")) {
                vo.setEndTime(DateTimeUtils.addDate(vo.getStartTime(), Integer.parseInt(vo.getEndTimeVal())));
            } else {
                vo.setEndTime(null);
            }
            if (vo.getWarnTimeVal() != null && !vo.getWarnTimeVal().equals("0")) {
                vo.setWarnTime(DateTimeUtils.addDate(vo.getStartTime(), Integer.parseInt(vo.getWarnTimeVal())));
            } else {
                vo.setWarnTime(null);
            }
        } else {
            vo.setEndTime(null);
            vo.setWarnTime(null);
        }

    }

    @Override
    public void updateStatus(UserPaperVO vo) {
        if (NumberUtil.isEmpty(vo.getId())) {
            throw new JeecgBootException("参数有问题！");
        }
        UserPaper userPaper = this.baseMapper.selectById(vo.getId());
        userPaper.setStatus(vo.getStatus());
        this.baseMapper.updateById(userPaper);
        if (userPaper.getStatus() == 2) {
            //且通过次数不小于规定的次数即为通过
            int passFlag = userPaper.getPassedTimes() >= userPaper.getTotalTimes() ? 2 : 1;
            baseMapper.update(null, new LambdaUpdateWrapper<UserPaper>().set(UserPaper::getExamResults, passFlag)
                    .eq(UserPaper::getId, userPaper.getId()));
        } else {
            baseMapper.update(null, new LambdaUpdateWrapper<UserPaper>().set(UserPaper::getExamResults, null)
                    .eq(UserPaper::getId, userPaper.getId()));
        }
        syncStateService.resetSyncState(CommonConstant.USER_PAPER, userPaper.getId().toString());
    }

    @Override
    public void reset(Long id) {
        if (NumberUtil.isEmpty(id)) {
            throw new JeecgBootException("参数有问题！");
        }
        //把修改后的数据放入数据库中
        this.baseMapper.update(null, new LambdaUpdateWrapper<UserPaper>()
                .set(UserPaper::getStatus, 0)
                .set(UserPaper::getDoneTimes, 0)
                .set(UserPaper::getPassedTimes, 0)
                .set(UserPaper::getFailedTimes, 0)
                .set(UserPaper::getTotalDuration, 0L)
                .set(UserPaper::getExamResults, 0)
                .eq(UserPaper::getId, id));
        //数据发生修改之后，需要将同步的数据改为未同步，也就是将同步状态改为0
        syncStateService.resetSyncState(CommonConstant.USER_PAPER, String.valueOf(id));

        LambdaUpdateWrapper<Exam> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.setSql("state=state+50").lt(Exam::getState, 50).eq(Exam::getUserPaperId, id);
        examMapper.update(null, lambdaUpdateWrapper);

    }

    @Override
    public void resetBatch(List<Long> idList) {
        for (Long id : idList) {
            reset(id);
        }
    }

    @Override
    public void fixPaperPushStatus(Long paperId) {
        baseMapper.fixPaperPushStatus(paperId);
    }

    @Override
    public UserPaper getTop(LambdaQueryWrapper<UserPaper> queryWrapper) {
        return baseMapper.getTop(queryWrapper);
    }

    private void doSave(UserPaperVO vo) {
        UserPaper userPaper = new UserPaper();
        BeanUtils.copyProperties(vo, userPaper);
        this.saveOrUpdate(userPaper);
    }


    @Override
    public IPage<TraineeVO> listExamWarnList(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper) {
        return baseMapper.listExamWarnList(page, queryWrapper);
    }

    @Override
    public List<PaperVO> findPaperList(String userId) {
        return baseMapper.findPaperList(userId);
    }
}
