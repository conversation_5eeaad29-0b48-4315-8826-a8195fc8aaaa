package org.jeecg.modules.training.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.training.entity.ImportSysUser;
import org.jeecg.modules.training.entity.Team;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.vo.MiniProgramDTO;
import org.jeecg.modules.training.vo.SysTraineeVO;
import org.jeecg.modules.training.vo.TraineeRegisterDTO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Description: 受试人信息表
 */
public interface TraineeService extends IService<Trainee> {
    Result<?> importExcelForAll(HttpServletRequest request) throws IOException;

    TraineeVO getUserByIDCard(String identityCard);

    /**
     * 获取受试人信息
     *
     * @param page         分页
     * @param loginUserId  导入人Id
     * @param queryWrapper
     * @param sysDepartId
     * @param memberType
     * @return
     */
    IPage<SysTraineeVO> getTraineeList(Page<TraineeVO> page, String loginRoute, String loginUserId, QueryWrapper<SysTraineeVO> queryWrapper, String sysDepartId, Integer memberType);

    /**
     * 单船导入
     *
     * @param request
     * @return
     * @throws IOException
     */
    Result<?> importExcelForDepart(HttpServletRequest request) throws IOException;

    /**
     * 根据用户名获取用户信息
     *
     * @param username
     * @return
     */
    Trainee getUserByName(String username);

    /**
     * 删除用户
     *
     * @param userId
     * @param sysDepart
     * @return
     */
    boolean deleteUser(String userId, SysDepart sysDepart);

    @CacheEvict(value = {CacheConstant.TRAINEE_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    boolean deleteBatchUsers(List<String> traineeIdList, SysDepart sysDepart);

    /**
     * 受试人编辑
     *
     * @param userList
     * @param errorMessage
     * @return
     * @throws IOException
     */
    Result<?> importUsers(List<ImportSysUser> userList, List<String> errorMessage) throws IOException;

    /**
     * 修改密码
     *
     * @param trainee
     * @return
     */
    Result<?> changePassword(Trainee trainee);

    /**
     * 导出受试人信息查询
     *
     * @param loginUserId
     * @param queryWrapper
     * @param sysDepartId
     * @param memberType
     * @return
     */
    List<SysTraineeVO> getTraineeList(String loginRoute, String loginUserId, QueryWrapper<SysTraineeVO> queryWrapper, String sysDepartId, Integer memberType);

    /**
     * 校验用户是否有效
     *
     * @param trainee
     * @return
     */
    Result checkUserIsEffective(Trainee trainee);

    /**
     * 根据用户名设置部门ID
     *
     * @param username
     * @param orgCode
     */
    void updateUserDepart(String username, String orgCode);

    /**
     * 组装登录后的数据
     *
     * @param trainee
     * @param response
     * @return
     */
    JSONObject appendInfo(Trainee trainee, HttpServletResponse response);

    /**
     * 根据手机号获取用户名和密码
     *
     * @param phone 手机号
     */
    Trainee getUserByPhone(String phone);

    /**
     * 恢复逻辑删除
     *
     * @param traineeId 主键id
     * @param trainee   受试人
     */
    void revertLogicDeleted(String traineeId, Trainee trainee);

    Result<?> resetPassword(String username, String oldpassword, String password);

    IPage<Trainee> queryDepartTraineePageList(String departId, String id, String username, String realname, Integer pageSize, Integer pageNo);

    IPage<TraineeVO> getExamUserForPhase(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper);

    IPage<TraineeVO> getExamUserForPhaseItem(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper);

    Trainee getUserByOpenId(String openId);

    Trainee signInUser(MiniProgramDTO miniProgramDTO);

    // 解除受试人和openId的关系
    void unLinkTraineeToOpenId(Long id);

    List<TraineeVO> getAllExamUserForPhase(QueryWrapper<TraineeVO> wrapper);

    // 新增或编辑受试人时，设置受试人信息
    void setTraineeInfo(JSONObject jsonObject, ImportSysUser importSysUser);

    List<Trainee> selectByIds(List<String> list);

    // 船员注册
    Trainee register(TraineeRegisterDTO traineeRegisterDTO, SysDepart sysDepart, Team team);

    // 获取即将过期（一个月内到期）和已经到期的会员数量
    JSONArray getWillAndExpireMemberCount();

    // 企业会员模式下，对已有船员执行会员数量扣除操作
    JSONArray deductMember();

    // 检查套餐是否过期，并处理船员会员过期的情况
    void checkPackagesExpire();

    // 船员开通船员会员
    void openMember(Trainee trainee, Long ordersId);

    // 删除船员部门关系前执行的操作
    void onTraineeDeleted(String userId, SysDepart sysDepart);
}
