package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: huazhengkan
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Mapper
public interface UserPaperMapper extends BaseMapper<UserPaper> {

    List<PaperVO> findPapers(@Param("userId") String userId, @Param("categoryId") Long categoryId);

    Integer updateExamResult(@Param("id") Long id, @Param("success") Integer success, @Param("fail") Integer fail, @Param("duration") Long duration);

    /**
     * 处理告警
     *
     * @param now
     */
    void processWarnStatus(@Param("now") String now);

    /**
     * 处理过期
     *
     * @param now
     */
    void processExpiredStatus(@Param("now") String now);

    /**
     * 找出按paper规则，应该要推送，但还没有推送的组合
     *
     * @return
     */
    List<UserPaper> findNotPushCombination();

    /**
     * 找出需要推给这些用户的
     *
     * @param userIds
     * @return
     */
    List<UserPaper> findUserPush(@Param("userIds") String userIds);

    /**
     * 找出指定考试需要推给的人员
     *
     * @param paperId
     * @return
     */
    List<UserPaper> findPaperPush(@Param("paperId") Long paperId);

    IPage<UserPaperVO> listPages(Page<UserPaperVO> page, @Param("ew") QueryWrapper<UserPaperVO> queryWrapper);

    IPage<TraineeVO> listExamWarnList(Page<TraineeVO> page, @Param("ew") QueryWrapper<TraineeVO> queryWrapper);

    void fixPaperPushStatus(@Param("paperId") Long paperId);

    UserPaper getTop(@Param("ew") LambdaQueryWrapper<UserPaper> queryWrapper);

    int resetUserPaper(@Param("userId") String userId);

    List<PaperVO> findPaperList(@Param("userId") String userId);
}
