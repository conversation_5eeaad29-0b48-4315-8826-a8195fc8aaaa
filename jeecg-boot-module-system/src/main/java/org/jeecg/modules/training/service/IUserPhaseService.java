package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.entity.UserPhase;

/**
 * <AUTHOR>
 * @description 针对表【user_phase(用户阶段关系)】的数据库操作Service
 * @createDate 2023-09-23 14:35:17
 */
public interface IUserPhaseService extends IService<UserPhase> {

    void setCurrentUserPhase(UserPhase userPhase);

    int resetUserPhase(Trainee trainee);

}
