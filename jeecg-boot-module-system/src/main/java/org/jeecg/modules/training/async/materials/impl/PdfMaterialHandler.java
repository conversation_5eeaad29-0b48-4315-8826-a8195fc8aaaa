package org.jeecg.modules.training.async.materials.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDDocumentOutline;
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDOutlineItem;
import org.apache.pdfbox.text.PDFTextStripper;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.NumberUtil;
import org.jeecg.modules.training.async.materials.MaterialHandler;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年09月23日 13:21
 */
@Slf4j
@Component
@Scope("prototype")
public class PdfMaterialHandler extends MaterialHandler {

    public PdfMaterialHandler() {
        super();
    }

    @Override
    public void run() {
        Date start = new Date();

        try {
            sysBaseAPI.sendSysAnnouncement(new MessageDTO("system", "admin", "学习资料处理", "PDF处理开始：" + study.getName() + "," + dateFormat.format(start)));

            if (args != null && args.length != 0 && args[0].equals("compressOnly")) {
                //根据compressDpi的大小压缩现有的文件并且将原有的content字段的值替换为指定的压缩位置
                if (study.getCompressDpi() != null && !NumberUtil.equals(study.getCompressDpi(), 0)) {
                    String saveFile = FilenameUtils.removeExtension(orgFilePath) + ".pdf";
                    File outputFile = new File(saveFile);
                    //压缩pdf文件
                    handlePdf(outputFile, study);
                }

                return;
            }

            if (orgFilePath.toLowerCase().endsWith(".pdf")) {
                finish();
                // 生成书签json文件当前版本暂时修复报错json文件不存在问题  todo
                addBookmarkExistingPdfDocument(orgFilePath);
                return;
            }

            String saveFile = FilenameUtils.removeExtension(orgFilePath) + ".pdf";
            File outputFile = new File(saveFile);

            try {
                if (!outputFile.getAbsolutePath().equalsIgnoreCase(orgFilePath) && outputFile.exists()) {
                    outputFile.delete();
                }
            } catch (Exception e) {
                log.error("删除失败");
            }

            try (FileInputStream fileInputStream = new FileInputStream(orgFilePath);
                 FileOutputStream fileOutputStream = new FileOutputStream(saveFile)
            ) {

                /**
                 XWPFDocument xwpfDocument = new XWPFDocument(fileInputStream);
                 PdfOptions pdfOptions = PdfOptions.create();

                 PdfConverter.getInstance().convert(xwpfDocument, fileOutputStream, pdfOptions);
                 **/

                // 初始化 Aspose.Words
                AsposeWordsInitializer.getInstance().initialize();

                Document doc = new Document(fileInputStream);

                //4.将文档对象保存为Pdf格式
                doc.save(fileOutputStream, SaveFormat.PDF);

                addBookmarkExistingPdfDocument(saveFile);

                finish();

            } catch (Exception ex) {
                log.error("Aspose初始化失败", ex);
            }
        } finally {
            Date end = new Date();
            String msg = "PDF处理完成：" + study.getName() + "," + dateFormat.format(start) + "~" + dateFormat.format(end) + "，耗时：" + (end.getTime() - start.getTime()) / 1000 + "秒";
            sysBaseAPI.sendSysAnnouncement(new MessageDTO("system", "admin", "学习资料处理", msg));
            addStudyToES(study);
        }
    }

    private void finish() {

        String saveFile = FilenameUtils.removeExtension(orgFilePath) + ".pdf";
        File outputFile = new File(saveFile);

        LambdaUpdateWrapper<Study> updateWrapper = new LambdaUpdateWrapper<>();

        updateWrapper
                .set(Study::getSize, outputFile.length())
                .set(Study::getContent, saveFile.substring(dataRoot.length() + 1))
                .set(Study::getFilePath, orgFilePath.substring(dataRoot.length() + 1))
                .and(q -> q.eq(Study::getId, study.getId()).or().eq(Study::getShareId, study.getId()));
        studyService.update(updateWrapper);

        //根据compressDpi的大小压缩现有的文件并且将原有的content字段的值替换为指定的压缩位置
        if (study.getCompressDpi() != null && !NumberUtil.equals(study.getCompressDpi(), 0)) {
            //压缩pdf文件
            handlePdf(outputFile, study);
        }
        // 共享学习资料
        shareStudy(study, username);
    }

    private void addBookmarkExistingPdfDocument(String filePath) {
        File file = new File(filePath);

        PDDocument pdDocument = null;

        try {

            pdDocument = PDFBoxUtils.load(file);

            if (pdDocument == null) {
                return;
            }
            PDDocumentCatalog documentCatalog = pdDocument.getDocumentCatalog();
            if (documentCatalog == null) {
                return;
            }

            List<PdfBoxData> allBookList = new ArrayList<>();
            int numberOfPages = pdDocument.getNumberOfPages();

            //以最后一次获取到目录的页码作为偏移量
            int add = 0;

            //只读取前十页，以找到目录的最后一页的后一页为第一页
            for (int i = 1; i <= (Math.min(numberOfPages, CommonConstant.PDF_PAGE_NUMBER)); i++) {
                //allBookList.addAll(PDFBoxUtils.getPdfBoxTextList(pdDocument, i));
                //文本剥离器
                PDFTextStripper stripper = new PDFTextStripper();
                //按页进行读取，页码从1开始
                stripper.setStartPage(i);
                stripper.setEndPage(i);
                //按位置进行排序
                stripper.setSortByPosition(true);
                //获取文本
                String text = stripper.getText(pdDocument);
                //linux换行符是\n，windows是\r\n
                //String[] dataArr = text.split("\r\n");
                String[] dataArr = text.split("\n");
                List<PdfBoxData> pdfBoxDataList = new ArrayList<>();
                for (String data : dataArr) {
                    //log.info("{}", data);
                    data = data.trim();
                    if (data.matches(UtilsEnums.TOC_REGEX.getCode())) {
                        //add的值是最后匹配到目录的页数
                        add = i;
                        log.info(data + "--匹配到了");
                        String[] split = data.split("\\.{3,}");
                        pdfBoxDataList.add(new PdfBoxData(split[0], Integer.parseInt(split[1])));
                    } else {
                        log.info(data + "未匹配到");
                    }
                }
                allBookList.addAll(pdfBoxDataList);
            }

            //存储代码生成的目录结构
            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("title", "代码生成的目录");
            jsonObject1.put("directory", allBookList);
            jsonArray.add(jsonObject1);
            //jsonObject1.put("代码生成的目录", allBookList);

            PDDocumentOutline documentOutline = documentCatalog.getDocumentOutline();
            //当书签存在的时候,不需要覆盖书签,但还是需要存储原始pdf的书签Json数据
            if (documentOutline != null && documentOutline.getOpenCount() != 0) {
                List<PdfBoxData> sourceBookList = new ArrayList<>();
                PDOutlineItem current = documentOutline.getFirstChild();
                while (current != null) {
                    PDPage currentPage = current.findDestinationPage(pdDocument);
                    int pageNumber = documentCatalog.getPages().indexOf(currentPage) + 1 - add;
                    //PDPageDestination destination = (PDPageDestination) current.getDestination();
                    //int pageNumber = destination.getPageNumber();
                    //current.getDestination().get
                    String title = current.getTitle();
                    sourceBookList.add(new PdfBoxData(title, pageNumber));
                    current = current.getNextSibling();
                }
                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put("title", "初始目录");
                jsonObject2.put("directory", sourceBookList);
                //jsonObject2.put("初始目录", sourceBookList);
                jsonArray.add(jsonObject2);
            } else {
//                PDFBoxUtils.addMarkBook(pdDocument, allBookList, filePath, add);
            }

            String jsonFilePath = FilenameUtils.removeExtension(filePath) + ".json";
            File jsonFile = new File(jsonFilePath);
            if (!jsonFile.exists()) {
                jsonFile.createNewFile();
            }
            try (OutputStream os = new FileOutputStream(jsonFile)) {
                BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(os, "utf-8"));
                bw.write(jsonArray.toJSONString());
            } catch (Exception ex) {
                log.error("发生json写入异常", ex);
                //
            }
            /*Path ConfPath = null;
            try {
                ConfPath = Paths.get(new URI(FilenameUtils.removeExtension(filePath) + ".json"));
                if (!Files.exists(ConfPath)){
                    Files.createFile(ConfPath);
                }
                Files.write(ConfPath
                        , jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8)
                        , StandardOpenOption.TRUNCATE_EXISTING);
            } catch (Exception e) {
                e.printStackTrace();
            }*/


        } catch (IOException e) {
            log.error("目录或书签异常" + e.getMessage());
        } finally {
            PDFBoxUtils.close(pdDocument);
        }
    }


    /**
     * 根据compressDpi的大小压缩现有的文件并且将原有的content字段的值替换为指定的压缩位置
     *
     * @param file
     * @param study
     */
    private void handlePdf(File file, Study study) {
        String filePath = study.getFilePath();
        String compressContent = filePath.substring(0, filePath.indexOf(FileUtil.getName(filePath))) + "thum_" + FileUtil.getName(filePath);
        File compressFile = new File(dataRoot + File.separator + compressContent);
        if (!compressFile.getParentFile().exists()) {
            compressFile.getParentFile().mkdirs();
        }
        /*//删除掉原来的压缩文件
        if (compressFile.exists()) {
            compressFile.delete();
        }*/
        //压缩原始的pdf文件到指定位置
        CompressUtil.CompressPdf(file, compressFile, study.getCompressDpi());
        //设置将Content的值由原始文件的路径替换为指定的压缩路径
        study.setContent(compressContent);
    }


}
