package org.jeecg.modules.training.controller.admin;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.fields.FieldFilter;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.share.PushShareService;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.training.util.ExamUtils;
import org.jeecg.modules.training.util.FindsCategoryChildrenUtil;
import org.jeecg.modules.training.vo.CategoryTreeVO;
import org.jeecg.modules.training.vo.CategoryVO;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 分类
 * @Author: huazhengkan
 * @Date: 2022-05-04
 * @Version: V1.0  字段排序
 */
@Slf4j
@Api(tags = "分类")
@RestController
@RequestMapping("/adm/category")
public class CategoryController extends JeecgController<Category, ICategoryService> {
    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private TraineeService traineeService;

    @Autowired
    private IStudyService studyService;

    @Autowired
    private IStudyLogService studyLogService;

    @Value("${jeecg.path.icons}")
    private String iconsRootPath;

    @Autowired
    private IUserPaperService userPaperService;

    @Resource
    private ISyncStateService syncStateService;

    @Autowired
    private ISysDepartService sysDepartService;

    @Autowired
    private IIndustryService industryService;

    @Autowired
    private PushShareService pushShareService;

    private Random random = new Random();

    /**
     * 分页列表查询
     * 变成属性结构
     *
     * @param req
     * @return
     */
    @AutoLog(value = "树形分类-分页列表查询")
    @ApiOperation(value = "树形分类-分页列表查询", notes = "树形分类-分页列表查询")
    @GetMapping(value = "/pagelist")//TODO：后台页面使用改为pageLis
    @FieldFilter({"description", "categoryId", "categoryName", "content", "createBy", "createTime", "delFlag", "depRoute", "id", "industryIds", "industryNames", "title", "updateBy", "updateTimeappMenu", "createBy", "createTime", "delFlag", "depRoute", "disabled", "icon", "id", "industryNames", "key", "name", "phaseLevel", "route", "shareMode", "type", "updateBy", "updateTime", "value",})
    public Result<List<CategoryTreeVO>> queryPageList(Category category, HttpServletRequest req) {
        Result<List<CategoryTreeVO>> result = new Result<>();
        try {
            // 1. 获取原始请求参数，并创建一个可修改的Map
            Map<String, String[]> parameterMap = new HashMap<>(req.getParameterMap());
            // 2. 检查是否需要修正排序字段名（industryNames 映射为 industryIds）
            if("industryNames".equals(req.getParameter("column"))){
                // 修正排序字段名为数据库实际列名 industryIds
                parameterMap.put("column", new String[]{"industryIds"});
            }
            QueryWrapper<Category> queryWrapper = QueryGenerator.initQueryWrapper(category, parameterMap);

            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            queryWrapper.and(q -> q.lambda().eq(Category::getDepRoute, loginUser.getDepRoute()));

            List<Category> list = categoryService.list(queryWrapper);
            list.forEach(item -> {
                item.setIndustryNames(industryService.getNamesByIndustryIds(item.getIndustryIds()));
            });

            List<CategoryTreeVO> treeList = FindsCategoryChildrenUtil.wrapTreeDataToTreeList(list);
            result.setResult(treeList);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @PostMapping("/autoSort")
    public Result autoSort() {
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        //不要删除的
        queryWrapper.eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.isNull(Category::getParentId);
        queryWrapper.orderByAsc(Category::getShowOrder);
        queryWrapper.orderByDesc(Category::getId);

        List<Category> firstStageCategorieList = categoryService.list(queryWrapper);
        Map<String, List<Category>> collect = firstStageCategorieList.stream().collect(Collectors.groupingBy(Category::getAppMenu));
        collect.forEach((k, firstStageCategories) -> {
            for (int i = 1; i <= firstStageCategories.size(); i++) {
                Category category = firstStageCategories.get(i - 1);
                category.setShowOrder(i);
                setShowOrder(category);
                categoryService.updateById(category);
                syncStateService.resetSyncState(CommonConstant.CATEGORY, category.getId().toString());
            }
            //categoryService.updateBatchById(firstStageCategories);
        });
        return Result.ok("设置顺序成功");
    }

    private void setShowOrder(Category category) {
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Category::getParentId, category.getId());
        wrapper.orderByAsc(Category::getShowOrder);
        wrapper.orderByDesc(Category::getId);
        List<Category> categories = categoryService.list(wrapper);
        if (categories == null || categories.size() == 0) {
            return;
        } else {
            for (int i = 1; i <= categories.size(); i++) {
                Category category1 = categories.get(i - 1);
                category1.setShowOrder(i);
                setShowOrder(category1);
                categoryService.updateById(category1);
                syncStateService.resetSyncState(CommonConstant.CATEGORY, category1.getId().toString());
            }
        }
    }

    /**
     * 添加
     *
     * @param category
     * @return
     */
    @AutoLog(value = "分类-添加")
    @ApiOperation(value = "分类-添加", notes = "分类-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Category category) {
        Category parentCategory = null;

        if (StringUtils.isEmpty(category.getShareMode())) {
            category.setShareMode("0");
        }

        if (category.getParentId() != null) {
            parentCategory = categoryService.getById(category.getParentId());
            if (!parentCategory.getIndustryIds().contains(industryService.getIndustryIdsByNames(category.getIndustryNames())))
                return Result.error("添加失败，所选行业超出父级行业范围");
            if (!"1".equals(parentCategory.getShareMode()) && "1".equals(category.getShareMode())) {
                return Result.error("父级分类未分享，子分类也无法分享");
            }
        }

        category.setDelFlag(CommonConstant.DEL_FLAG_0);

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        category.setDepRoute(loginUser.getDepRoute());

        String industryIds = industryService.getIndustryIdsByNames(category.getIndustryNames());
        if (category.getIndustryNames() != null && !category.getIndustryNames().trim().isEmpty()) {
            category.setIndustryIds(industryIds);
        }

        categoryService.save(category);
        //添加分类路径
        if (parentCategory != null) {
            if (parentCategory.getRoute() == null) parentCategory.setRoute("");
            category.setRoute(parentCategory.getRoute() + "/" + category.getId());
        } else {
            category.setRoute("/" + category.getId().toString());
        }
        categoryService.updateById(category);

        //复制数据
        if ("admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(category.getIndustryNames()) && "1".equals(category.getShareMode())) {

            pushShareService.pushData("category", category.getId().toString());

        }

        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param category
     * @return
     */
    @AutoLog(value = "分类-编辑")
    @ApiOperation(value = "分类-编辑", notes = "分类-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody Category category) {

        //是否是父级
        boolean flag = false;
        if (category.getParentId() != null) {
            Category parentCategory = categoryService.getById(category.getParentId());
            if (!parentCategory.getIndustryIds().contains(industryService.getIndustryIdsByNames(category.getIndustryNames())))
                return Result.error("修改失败，所选行业超出父级行业范围");
            if (!"1".equals(parentCategory.getShareMode()) && "1".equals(category.getShareMode())) {
                return Result.error("父级分类未分享，子分类也无法分享");
            }
        } else {
            flag = true;
        }
        category.setDelFlag(CommonConstant.DEL_FLAG_0);
        //TOdo:为null值时不更新数据库

        Category categoryDb = service.getById(category.getId());
        if (!Objects.equals(categoryDb.getParentId(), category.getParentId())) {
            String newCatRoute;
            /*
             * 修改部门路径
             */
            Long parentId = category.getParentId();
            if (parentId != null) {
                //flag = true;
                Category parentCat = service.getById(parentId);
                newCatRoute = parentCat.getRoute() + "/" + category.getId();
            } else {
                newCatRoute = "/" + category.getId();
            }

            //修改当前分类下所有分类的路径
            LambdaQueryWrapper<Category> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            categoryLambdaQueryWrapper.likeRight(Category::getRoute, categoryDb.getRoute());
            List<Category> categoryList = service.list(categoryLambdaQueryWrapper);

            //遍历修改其子部门路径
            for (Category subCat : categoryList) {
                //假设 categoryDb.getId() = 3
                //假设 parentCat.getRoute()=/7/8
                //则有 newCatRoute = /7/8/3
                //假设 subRoute为  /1/2/3/4
                //假设 subRoute为  /1/2/3/5
                String subRoute = subCat.getRoute();
                int idx = subRoute.lastIndexOf(categoryDb.getId().toString());
                if (idx > 0) {
                    //=>7/8/3/4
                    //=>7/8/3/5
                    String newSubRoute = newCatRoute + subRoute.substring(idx + categoryDb.getId().toString().length());
                    subCat.setRoute(newSubRoute);
                    service.updateById(subCat);
                    syncStateService.resetSyncState(CommonConstant.CATEGORY, subCat.getId().toString());
                }
            }

            category.setRoute(newCatRoute);
        }

        if (category.getIndustryNames() != null && !category.getIndustryNames().trim().isEmpty()) {
            category.setIndustryIds(industryService.getIndustryIdsByNames(category.getIndustryNames()));
        }

        categoryService.updateById(category);

        syncStateService.resetSyncState(CommonConstant.CATEGORY, category.getId().toString());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String oldShareMode = categoryDb.getShareMode();

        //复制数据,sharemode修改成1的时候才需要修改
        if ("admin".equals(loginUser.getUsername()) && "1".equals(category.getShareMode())) {

            pushShareService.pushData("category", category.getId().toString());

        }

        if (flag && "admin".equals(loginUser.getUsername()) && StringUtils.isNotBlank(category.getIndustryNames()) && !"1".equals(category.getShareMode()) && "1".equals(oldShareMode)) {
            //修改所有子层级的shareMode,只修改shareMode为1的
            categoryService.update(new LambdaUpdateWrapper<Category>().eq(Category::getShareMode, 1).likeRight(Category::getRoute, category.getRoute()).set(Category::getShareMode, category.getShareMode()));
        }
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "分类-通过id删除")
    @ApiOperation(value = "分类-通过id删除", notes = "分类-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        if (categoryService.removeById(id)) {
            syncStateService.resetSyncState(CommonConstant.CATEGORY, id);
            return Result.ok();
        }

        return Result.error("删除失败");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "分类-批量删除")
    @ApiOperation(value = "分类-批量删除", notes = "分类-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        if (ids == null || ids.trim().isEmpty()) {
            return Result.error("参数不识别！");
        }

        List<String> collect = Arrays.stream(ids.split(",")).distinct().collect(Collectors.toList());

        if (categoryService.removeBatchByIds(collect)) {
            collect.forEach(item -> syncStateService.resetSyncState(CommonConstant.CATEGORY, item));
            return Result.ok();
        }

        return Result.error("删除失败");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "分类-通过id查询")
    @ApiOperation(value = "分类-通过id查询", notes = "分类-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Category category = categoryService.getById(id);
        CategoryVO categoryVo = new CategoryVO();
        BeanUtils.copyProperties(category, categoryVo);

        LambdaQueryWrapper<Category> queryChildren = new LambdaQueryWrapper<Category>().eq(Category::getParentId, category.getId()).eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);
        categoryVo.setHasChildren(categoryService.count(queryChildren) > 0);

        return Result.OK(categoryVo);
    }

    /**
     * 获取所有分类
     * huazhengkan
     *
     * @return
     */
    @ApiOperation("获取所有分类接口")
    @RequestMapping(value = "/listByType", method = RequestMethod.GET)
    public Result<List<CategoryVO>> queryByType(@RequestParam(name = "id", required = false) String id, @RequestParam(name = "type", required = false) String type) {
        Result<List<CategoryVO>> result = new Result<>();
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<Category>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        query.and(q -> q.eq(Category::getDepRoute, loginUser.getDepRoute()));


        query.isNull(Category::getParentId);

        if (oConvertUtils.isNotEmpty(id)) {
            Object[] arr = id.split(",");
            query.in(Category::getId, arr);
        }

        if (oConvertUtils.isNotEmpty(type)) {
            if (type.contains(",")) {
                Object[] vals = Arrays.stream(type.split(",")).map(Integer::decode).toArray();
                query.in(Category::getType, vals);
            } else {
                query.eq(Category::getType, type);
            }
        }

        query.eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);
        query.orderByAsc(Category::getShowOrder);
        List<Category> categoryList = categoryService.list(query);

        if (categoryList == null || categoryList.size() <= 0) {
            result.error500("未找到分类");
        } else {
            String userId = loginUser.getId();
            Trainee trainee = traineeService.getUserByName(loginUser.getUsername());

            //上船时间
            String boardTime = ExamUtils.getUserBoardTimeName(trainee);

            //职务
            String post = "," + trainee.getPost() + ",";

            //组
            String groupInfo = trainee.getGroupInfo();
            if (StringUtils.isNotEmpty(groupInfo)) {
                groupInfo = "," + groupInfo + ",";
            }

            List<CategoryVO> list = new ArrayList<>();
            for (Category category : categoryList) {
                CategoryVO vo = fillCategoryVO(userId, boardTime, post, groupInfo, category);
                list.add(vo);
            }

            result.setResult(list);
            result.setSuccess(true);
            result.setCode(200);
        }
        return result;
    }

    /**
     * 获取所有分类
     * huazhengkan
     *
     * @return
     */
    @ApiOperation("获取所有分类接口")
    @RequestMapping(value = "/listByMenu", method = RequestMethod.GET)
    public Result<List<CategoryVO>> queryByMenu(@RequestParam(name = "menu") String menu) {
        Result<List<CategoryVO>> result = new Result<>();
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<Category>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        query.and(q -> q.eq(Category::getDepRoute, loginUser.getDepRoute()));


        query.isNull(Category::getParentId);

        if (menu.contains(",")) {
            Object[] vals = Arrays.stream(menu.split(",")).map(Integer::decode).toArray();
            query.in(Category::getAppMenu, vals);
        } else {
            query.eq(Category::getAppMenu, menu);
        }

        query.eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);

        query.orderByAsc(Category::getShowOrder);
        List<Category> categoryList = categoryService.list(query);

        if (categoryList == null || categoryList.size() <= 0) {
            result.error500("未找到分类");
        } else {
            String userId = loginUser.getId();
            Trainee trainee = traineeService.getUserByName(loginUser.getUsername());

            //上船时间
            String boardTime = ExamUtils.getUserBoardTimeName(trainee);

            //职务
            String post = "," + trainee.getPost() + ",";

            //组
            String groupInfo = trainee.getGroupInfo();
            if (StringUtils.isNotEmpty(groupInfo)) {
                groupInfo = "," + groupInfo + ",";
            }

            List<CategoryVO> list = new ArrayList<>();
            for (Category category : categoryList) {
                CategoryVO vo = fillCategoryVO(userId, boardTime, post, groupInfo, category);
                list.add(vo);
            }

            result.setResult(list);
            result.setSuccess(true);
            result.setCode(200);
        }
        return result;
    }

    /**
     * 根据父分类获取子分类
     * huazhengkan
     *
     * @return
     */
    @ApiOperation("根据父分类获取子分类")
    @RequestMapping(value = "/childList", method = RequestMethod.GET)
    public Result<List<CategoryVO>> queryChildList(@RequestParam(name = "parentId", required = false) String parentId) {
        Result<List<CategoryVO>> result = new Result<>();
        LambdaQueryWrapper<Category> query = new LambdaQueryWrapper<Category>();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        query.and(q -> q.eq(Category::getDepRoute, loginUser.getDepRoute()));


        query.eq(Category::getParentId, parentId);
        query.eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);
        query.orderByAsc(Category::getShowOrder);
        List<Category> categoryList = categoryService.list(query);

        if (categoryList == null || categoryList.size() <= 0) {
            result.error500("未找到子分类");
        } else {

            String userId = loginUser.getId();
            Trainee trainee = traineeService.getUserByName(loginUser.getUsername());

            //上船时间
            String boardTime = ExamUtils.getUserBoardTimeName(trainee);

            //职务
            String post = "," + trainee.getPost() + ",";

            //组
            String groupInfo = trainee.getGroupInfo();
            if (StringUtils.isNotEmpty(groupInfo)) {
                groupInfo = "," + groupInfo + ",";
            }

            List<CategoryVO> list = new ArrayList<>();
            for (Category category : categoryList) {
                CategoryVO vo = fillCategoryVO(userId, boardTime, post, groupInfo, category);
                list.add(vo);
            }

            result.setResult(list);
            result.setSuccess(true);
            result.setCode(200);
        }
        return result;
    }


    @NotNull
    private CategoryVO fillCategoryVO(String userId, String boardTime, String post, String groupInfo, Category category) {

        int type = category.getType();

        CategoryVO vo = new CategoryVO();

        org.springframework.beans.BeanUtils.copyProperties(category, vo);

        LambdaQueryWrapper<Category> queryChildren = new LambdaQueryWrapper<Category>().eq(Category::getParentId, category.getId()).eq(Category::getDelFlag, CommonConstant.DEL_FLAG_0);

        //TODO 此处可能会有性能问题，category 数据量比较大时，会有点慢，可以通过 增加ParentId索引提高性能
        // 通常子分类不会很多，初步预计10-20个就很多了。
        // 暂时忽略此处性能问题
        vo.setHasChildren(categoryService.count(queryChildren) > 0);

        //0:考试;1:检测类;2:许可证类;3:学习
        switch (type) {
            case 0:  //考试

                //查询当前用户的每个分类是否都考试过,考试的
                List<PaperVO> papers = userPaperService.findPapers(userId, category.getId());
                int remainderExams = 0, numberExams = 0, notPassed = 0;
                if (!papers.isEmpty()) {

                    // 只统计 考试 并且 应考次数 大于 0 的
                    // 练习和可以无限次考试的，无需统计
                    for (PaperVO p : papers) {

                        if (p.getType() == 2 && p.getTotalTimes() > 0) {

                            fixPaperNumbers(p);

                            numberExams += p.getTotalTimes();

                            int allTimes = p.getTotalTimes() * (p.getRetryTimes() + 1);
                            int usedTimes = p.getPassedTimes() * (p.getRetryTimes() + 1) + p.getFailedTimes();

                            if (allTimes > usedTimes) {
                                //未完成数量
                                remainderExams += p.getTotalTimes() - p.getPassedTimes();
                            } else {
                                //应该通过但未通过的次数
                                //这里不要用 p.failedTimes，这个是总的失败次数
                                notPassed += p.getTotalTimes() - p.getPassedTimes();
                            }
                        }
                    }
                }

                vo.setRemainderExams(remainderExams);
                vo.setNumberExams(numberExams);
                vo.setNoPassed(notPassed);
                break;
            case 1://检测类
                break;
            case 2://许可证
                break;
            case 3://学习
                //查询当前用户在当前分类中未阅读和未关注的数量

                //先查询用户应该阅读的数量
                Long needReadNumber = studyService.getNeedReadNumber(userId, boardTime, post, groupInfo, category);
                //再查询用户已经阅读的数量
                Long alreadyReadNumber = studyLogService.getAlreadyReadNumber(userId, boardTime, post, groupInfo, category);
                vo.setUnread((int) Math.max(0, needReadNumber - alreadyReadNumber));

//                //查询用户需要关注的数量
//                Long needAttentionNumber = studyService.getNeedAttentionNumber(userId, boardTime, post, groupInfo, category);
//                //查询用户已经关注的数量
//                Long alreadyAttentionNumber = studyLogService.getAlreadyAttentionNumber(userId, boardTime, post, groupInfo, category);
//                vo.setNotFollowed((int) Math.max(0, needAttentionNumber - alreadyAttentionNumber));

                break;
        }

        return vo;
    }

    private void fixPaperNumbers(PaperVO p) {
        if (p.getRetryTimes() == null) p.setRetryTimes(0);
        if (p.getPassedTimes() == null) p.setPassedTimes(0);
        if (p.getDoneTimes() == null) p.setDoneTimes(0);
        if (p.getFailedTimes() == null) p.setFailedTimes(0);
    }


    /**
     * 查询数据 查出所有分类,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/queryTreeByKeyWord", method = RequestMethod.GET)
    @FieldFilter({"appMenu", "createBy", "delFlag", "depRoute", "description", "disabled", "icon", "id", "key",
            "route", "shareMode", "showOrder", "title", "type", "updateBy", "updateTime", "value"})
    public Result<Map<String, Object>> queryTreeByKeyWord(@RequestParam(name = "keyWord", required = false) String keyWord, @RequestParam(name = "type", required = false) String type) {
        Result<Map<String, Object>> result = new Result<>();
        try {
            Map<String, Object> map = new HashMap(5);
            List<CategoryTreeVO> list = categoryService.queryTreeByKeyWord(type, keyWord);
            map.put("list", list);
            result.setResult(map);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param category
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Category category) {
        return super.exportXls(request, category, Category.class, "分类");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Category.class);
    }


    /**
     * 获取随机的图标背景
     *
     * @param response
     * @return
     */
    @GetMapping("/getRandomBackground")
    public void getRandomBackground(HttpServletResponse response) {
        //获取背景图片所在目录路径的文件对象
        File dir = new File(iconsRootPath + File.separator + "background");

        //获取当前目录下的所有背景图片
        File[] listFiles = dir.listFiles((f) -> f.getName().endsWith(".png"));

        //随机获取一张图片
        if (listFiles != null && listFiles.length > 0) {

            File randomBg = listFiles[random.nextInt(listFiles.length)];

            //返回随机获取的背景图片
            try {
                response.setContentType("image/png");
                response.getOutputStream().write(FileUtil.readBytes(randomBg));
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 查询当前登录用户可获取的分享的公共资料列表 sharemodel = 2 ，行业相同
     */
    @GetMapping("/getShareList")
    public Result<?> getSharePaperList(Category category, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<Category> queryWrapper = QueryGenerator.initQueryWrapper(category, req.getParameterMap());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        queryWrapper.in("share_mode", 1, 2);

        // 获取该部门所属行业的id集合
        String industryIds = sysDepartService.getDepartByOrgCode(loginUser.getOrgCode()).getIndustryIds();
        if (!industryIds.isEmpty()) {
            // 格式化行业id字符串
            industryIds = industryService.formatIndustryIdsExp(industryIds);
            queryWrapper.apply("industry_ids regexp {0}", industryIds);
        }

        Page<Category> page = new Page<>(pageNo, pageSize);
        service.page(page, queryWrapper);

        return Result.ok(page);
    }


    /**
     * 设置用户选择的公共资料
     */
    @GetMapping("setShareCategory")
    public Result<?> setShareCategory(@RequestParam List<Long> ids) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 共享数据
        ids.forEach(item -> {
            service.shareCategory(item, loginUser.getDepRoute(), true);
        });

        return Result.ok("获取成功");
    }

}
