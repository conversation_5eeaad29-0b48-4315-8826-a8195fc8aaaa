package org.jeecg.modules.training.async.share;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.config.init.SpringBeanUtil;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.training.async.share.impl.PushShareAllHandler;
import org.jeecg.modules.training.async.share.impl.PushShareDataHandler;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年12月14日
 */
@Slf4j
@Component
public class PushShareService {

    private final ConcurrentLinkedQueue<PushShareHandler> pushHandlers = new ConcurrentLinkedQueue<>();

    private final Object syncPushLocker = new Object();

    private Thread handlerPushThread;

    private volatile Boolean running = false;

    private Date lastPushAllTime = new Date();

    @Resource
    @Lazy
    private ISysDepartService sysDepartService;

    public PushShareService() {
    }

    public void start() {

        running = true;

        handlerPushThread = new Thread("PushShareData Thread") {
            @Override
            public void run() {
                try {

                    while (running) {

                        try {

                            PushShareHandler handler;
                            while ((handler = pushHandlers.poll()) != null) {
                                try {
                                    handler.run();
                                    log.info("run push depart success total task:" + pushHandlers.size());
                                } catch (Exception ex) {
                                    log.error("EXP", ex);
                                }
                            }
                        } catch (Exception ex) {
                            log.error("EXP", ex);
                        }

                        synchronized (syncPushLocker) {
                            syncPushLocker.wait();
                        }

                    }
                } catch (Exception ex) {
                    log.error("EXP", ex);
                }
                log.info("PushShareData Thread stoped");
            }
        };

        handlerPushThread.setDaemon(true);
        handlerPushThread.start();

        log.info("PushShareDataService started");

        //启动便推送一次,界面上有触发按钮，改成手动
        //pushAll();

    }

    public void close() {

        running = false;

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

        try {
            handlerPushThread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        handlerPushThread = null;

        log.info("PushShareDataService Thread stoped");
    }

    public void pushAll() {
        log.info("push all");
        pushAll(false, false);
    }

    public void pushAll(boolean force) {
        log.info("push all");
        pushAll(false, force);
    }

    public void stopAll() {
        synchronized (pushHandlers) {
            pushHandlers.clear();
        }
    }

    public boolean pushAll(boolean checkRepeatTime, boolean force) {

        if (checkRepeatTime && (new Date().getTime() - lastPushAllTime.getTime()) < 60 * 1000) {
            log.info("push all fail interval too small");
            return false;
        }

        List<SysDepart> departList = sysDepartService.list(new LambdaQueryWrapper<SysDepart>().isNotNull(SysDepart::getParentId).ne(SysDepart::getParentId, ""));
        return pushDeparts(departList, checkRepeatTime, force);
    }

    public boolean pushDepart(SysDepart depart) {
        return pushDeparts(Collections.singletonList(depart));
    }

    public boolean pushDeparts(List<SysDepart> departList) {
        return pushDeparts(departList, false, false);
    }

    public boolean pushDeparts(List<SysDepart> departList, boolean checkRepeatTime, boolean force) {
        if (checkRepeatTime && (new Date().getTime() - lastPushAllTime.getTime()) < 60 * 1000) {
            return false;
        }

        lastPushAllTime = new Date();

        PushShareHandler pushHandler = SpringBeanUtil.getBean(PushShareAllHandler.class);
        pushHandler.setDepartList(departList);
        pushHandler.setForce(force);

        synchronized (pushHandlers) {
            pushHandlers.add(pushHandler);
        }

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

        log.info("start push departs success total task:" + pushHandlers.size());
        return true;
    }

    public boolean pushData(String tableName, String dataId) {
        return pushData(tableName, dataId, true);
    }

    public boolean pushData(String tableName, String dataId, boolean force) {

        List<SysDepart> departList = sysDepartService.list(new LambdaQueryWrapper<SysDepart>().isNotNull(SysDepart::getParentId).ne(SysDepart::getParentId, ""));
        PushShareDataHandler pushHandler = SpringBeanUtil.getBean(PushShareDataHandler.class);
        pushHandler.setDepartList(departList);
        pushHandler.setTableName(tableName);
        pushHandler.setDataId(dataId);
        pushHandler.setForce(force);

        synchronized (pushHandlers) {
            pushHandlers.add(pushHandler);
        }

        synchronized (syncPushLocker) {
            syncPushLocker.notifyAll();
        }

        return true;
    }

}
