package org.jeecg.modules.training.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.training.entity.MiniProgram;
import org.jeecg.modules.training.entity.Paper;
import org.jeecg.modules.training.entity.PaperQuestion;
import org.jeecg.modules.training.mapper.PaperMapper;
import org.jeecg.modules.training.mapper.PaperQuestionMapper;
import org.jeecg.modules.training.service.*;
import org.jeecg.modules.training.vo.PaperQuestionVO;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.QrCodeFile;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Service
public class PaperServiceImpl extends ServiceImpl<PaperMapper, Paper> implements IPaperService {

    @Autowired
    private PaperQuestionMapper paperQuestionMapper;

    @Resource
    private ISyncStateService syncStateService;

    @Autowired
    private IMiniProgramService imiProgramService;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IPaperQuestionService paperQuestionService;


    @Autowired
    private IRuleService ruleService;

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    @Override
    public Result<Paper> deleteById(String id) {
        Result<Paper> result = new Result<Paper>();
        Paper paper = this.getById(id);
        if (paper == null) {
            result.error500("未找到对应实体");
        } else {
            paper.setDelFlag(CommonConstant.DEL_FLAG_1);
            boolean ok = this.updateById(paper);
            if (ok) {
                result.success("删除成功!");
            }
        }
        return result;
    }

    /**
     * 逻辑批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public Result<Paper> deleteBatch(List<String> ids) {

        for (String id : ids) {
            Paper paper = this.getById(id);
            if (paper != null) {
                baseMapper.deleteById(id);
                syncStateService.resetSyncState(CommonConstant.PAPER, id);
            }
        }
        return Result.OK("批量删除成功！");
    }

    @Override
    public IPage<PaperVO> listPages(Page<PaperVO> page, QueryWrapper<PaperVO> queryWrapper) {
        return baseMapper.listPapers(page, queryWrapper);
    }

    @Override
    public List<PaperVO> queryAll(QueryWrapper<PaperVO> queryWrapper) {
        return baseMapper.listPapers(queryWrapper);
    }

    @Override
    public int sharePaper(Long paperId, String paperName, Boolean clearGroupName, Boolean clearSort) {
        Paper paper = baseMapper.selectById(paperId);

        //复制试卷库
        paper.setId(null);
        paper.setName(paperName);
        paper.setDisabled(true);

        int rlt = baseMapper.insert(paper);

        //复制题目列表
        LambdaQueryWrapper<PaperQuestion> pqQuery = new LambdaQueryWrapper<>();
        pqQuery.eq(PaperQuestion::getPaperId, paperId);
        List<PaperQuestion> list = paperQuestionMapper.selectList(pqQuery);
        for (PaperQuestion paperQuestion : list) {
            paperQuestion.setId(null);
            paperQuestion.setPaperId(paper.getId());
            paperQuestion.setCreateTime(new Date());
            if (clearGroupName != null && clearGroupName)
                paperQuestion.setGroupName(null);
            if (clearSort != null && clearSort)
                paperQuestion.setSort(null);
            paperQuestionMapper.insert(paperQuestion);
        }

        return rlt;
    }

    @Override
    public void checkPaper(Paper paper) {

        //尝试抽题
        switch (paper.getDrawType()) {
            case 1://随机抽取题目
                tryQuestionsByRandom(paper);
                break;
            case 2://按卷套抽取题目
                tryQuestionsByRandomGroup(paper);
                break;
            default:
                throw new JeecgBootException("抽题模式[" + paper.getDrawType() + "]设置错误，无法抽题！");
        }
    }

    private void tryQuestionsByRandomGroup(Paper paper) {

        //全部题目
        QueryWrapper<PaperQuestionVO> pq = new QueryWrapper<>(new PaperQuestionVO());
        pq.eq("paper_id", paper.getId());
        List<PaperQuestionVO> paperQuestionVOS = paperQuestionMapper.listQuestionsVO(pq);

        //找出全部卷套
        List<String> groups = paperQuestionVOS.stream().map(PaperQuestion::getGroupName).distinct().collect(Collectors.toList());
        groups.remove(null);
        groups.remove("");

        //随机选一个
        if (!groups.isEmpty()) {

            for (String group : groups) {

                List<PaperQuestionVO> listVO = paperQuestionVOS.stream().filter(q -> q.getGroupName().equals(group)).collect(Collectors.toList());
                List<PaperQuestion> paperQuestions = new ArrayList<>();

                //抽题
                if (paper.getRadioCount() == 0 && paper.getMultiCount() == 0 && paper.getJudgeCount() == 0 && paper.getInputCount() == 0) {
                    //否则直接使用全部题目
                    paperQuestions = new ArrayList<>(listVO);
                } else {
                    //如果有数量配置，则按数量配置重新抽题
                    if (paper.getRadioCount() > 0) {
                        List<PaperQuestion> list = listVO.stream().filter(q -> q.getType() == 1).collect(Collectors.toList());
                        paperQuestions.addAll(pickRandomQuestions(list, paper.getRadioCount(), "卷套[" + group + "]的单选题"));
                    }
                    if (paper.getMultiCount() > 0) {
                        List<PaperQuestion> list = listVO.stream().filter(q -> q.getType() == 2).collect(Collectors.toList());
                        paperQuestions.addAll(pickRandomQuestions(list, paper.getMultiCount(), "卷套[" + group + "]的多选题"));
                    }
                    if (paper.getJudgeCount() > 0) {
                        List<PaperQuestion> list = listVO.stream().filter(q -> q.getType() == 3).collect(Collectors.toList());
                        paperQuestions.addAll(pickRandomQuestions(list, paper.getJudgeCount(), "卷套[" + group + "]的判断题"));
                    }
                    if (paper.getInputCount() > 0) {
                        List<PaperQuestion> list = listVO.stream().filter(q -> q.getType() == 4).collect(Collectors.toList());
                        paperQuestions.addAll(pickRandomQuestions(list, paper.getInputCount(), "卷套[" + group + "]的填写题"));
                    }
                }

                if (paperQuestions.isEmpty())
                    throw new JeecgBootException("卷套[" + group + "]的抽题结果为空，请检查抽题设置和题目数量！");
            }

        } else {

            List<PaperQuestion> paperQuestions = new ArrayList<>();

            //抽题
            if (paper.getRadioCount() == 0 && paper.getMultiCount() == 0 && paper.getJudgeCount() == 0 && paper.getInputCount() == 0) {
                //否则直接使用全部题目
                paperQuestions = new ArrayList<>(paperQuestionVOS);
            } else {
                //如果有数量配置，则按数量配置重新抽题
                if (paper.getRadioCount() > 0) {
                    List<PaperQuestion> list = paperQuestionVOS.stream().filter(q -> q.getType() == 1).collect(Collectors.toList());
                    paperQuestions.addAll(pickRandomQuestions(list, paper.getRadioCount(), "卷套[<空白>]的单选题"));
                }
                if (paper.getMultiCount() > 0) {
                    List<PaperQuestion> list = paperQuestionVOS.stream().filter(q -> q.getType() == 2).collect(Collectors.toList());
                    paperQuestions.addAll(pickRandomQuestions(list, paper.getMultiCount(), "卷套[<空白>]的多选题"));
                }
                if (paper.getJudgeCount() > 0) {
                    List<PaperQuestion> list = paperQuestionVOS.stream().filter(q -> q.getType() == 3).collect(Collectors.toList());
                    paperQuestions.addAll(pickRandomQuestions(list, paper.getJudgeCount(), "卷套[<空白>]的判断题"));
                }
                if (paper.getInputCount() > 0) {
                    List<PaperQuestion> list = paperQuestionVOS.stream().filter(q -> q.getType() == 4).collect(Collectors.toList());
                    paperQuestions.addAll(pickRandomQuestions(list, paper.getInputCount(), "卷套[<空白>]的填写题"));
                }
            }

            if (paperQuestions.isEmpty())
                throw new JeecgBootException("卷套[<空白>]的抽题结果为空，请检查抽题设置和题目数量！");
        }
    }

    private void tryQuestionsByRandom(Paper paper) {
        QueryWrapper<PaperQuestionVO> pq = new QueryWrapper<>(new PaperQuestionVO());
        pq.eq("paper_id", paper.getId());
        List<PaperQuestionVO> paperQuestionVOS = paperQuestionMapper.listQuestionsVO(pq);

        List<PaperQuestion> paperQuestions;
        if (paper.getRadioCount() == 0 && paper.getMultiCount() == 0 && paper.getJudgeCount() == 0 && paper.getInputCount() == 0) {
            //这个试卷的全部题目
            paperQuestions = new ArrayList<>(paperQuestionVOS);
        } else {
            paperQuestions = new ArrayList<>();
            if (paper.getRadioCount() > 0) {
                paperQuestions.addAll(pickRandomQuestions(paperQuestionMapper.listQuestions(paper.getId(), 1), paper.getRadioCount(), "单选题"));
            }
            if (paper.getMultiCount() > 0) {
                paperQuestions.addAll(pickRandomQuestions(paperQuestionMapper.listQuestions(paper.getId(), 2), paper.getMultiCount(), "多选题"));
            }
            if (paper.getJudgeCount() > 0) {
                paperQuestions.addAll(pickRandomQuestions(paperQuestionMapper.listQuestions(paper.getId(), 3), paper.getJudgeCount(), "判断题"));
            }
            if (paper.getInputCount() > 0) {
                paperQuestions.addAll(pickRandomQuestions(paperQuestionMapper.listQuestions(paper.getId(), 4), paper.getInputCount(), "填写题"));
            }
        }

        if (paperQuestions.isEmpty())
            throw new JeecgBootException("抽题结果为空，请检查抽题设置和题目数量！");

    }

    private List<PaperQuestion> pickRandomQuestions(List<PaperQuestion> listQuestions, Integer pickCount, String type) {
        if (listQuestions.size() < pickCount) {
            throw new JeecgBootException("【" + type + "】题目数量" + (listQuestions.size()) + "不够设定的抽题数量：" + pickCount);
        }
        Collections.shuffle(listQuestions);
        return listQuestions.subList(0, pickCount);
    }

    @Override
    public void updateQrCodeStatus(QrCodeFile qrCodeFile, Long paperId) {

        // 获取对应的试卷对象
        Paper paper = this.getById(paperId);
        // 获取对应的微信小程序对象
        MiniProgram miniProgram = getMiniProgram(paperId, Long.valueOf(qrCodeFile.getId()));


        // 修改试卷对象中的二维码文件
        JSONArray qrCodeFiles = paper.getQrCodeFiles();
        List<QrCodeFile> qrCodeFileList = JSONArray.parseArray(qrCodeFiles.toJSONString(), QrCodeFile.class);
        qrCodeFileList.forEach(item -> {
            if (Objects.equals(item.getId(), qrCodeFile.getId())) {
                item.setStatus(qrCodeFile.getStatus());
            }
        });
        paper.setQrCodeFiles(JSONArray.parseArray(JSONObject.toJSONString(qrCodeFileList)));


        // 修改数据库中的小程序对象
        imiProgramService.update(new LambdaUpdateWrapper<MiniProgram>()
                .set(MiniProgram::getStatus, qrCodeFile.getStatus())
                .eq(MiniProgram::getId, miniProgram.getId()));
        // 修改数据库中的试卷对象
        this.updateById(paper);
    }

    @Override
    public void deleteQrCode(Long paperId, Long id) {
        // 获取对应的试卷对象
        Paper paper = this.getById(paperId);
        // 获取对应的微信小程序对象
        MiniProgram miniProgram = getMiniProgram(paperId, id);

        // 删除试卷对象中的二维码文件
        JSONArray qrCodeFiles = paper.getQrCodeFiles();
        List<QrCodeFile> qrCodeFileList = JSONArray.parseArray(qrCodeFiles.toJSONString(), QrCodeFile.class);
        for (QrCodeFile qrCodeFile : qrCodeFileList) {
            if (Objects.equals(qrCodeFile.getId(), id)) {
                qrCodeFileList.remove(qrCodeFile);
                break;
            }
        }
        paper.setQrCodeFiles(JSONArray.parseArray(JSONObject.toJSONString(qrCodeFileList)));

        // 删除数据库中的小程序对象
        imiProgramService.removeById(miniProgram.getId());
        // 修改数据库中的试卷对象
        this.updateById(paper);
    }

    // 根据试卷id和二维码id获取小程序对象
    private MiniProgram getMiniProgram(Long paperId, Long id) {
        return imiProgramService.getOne(new LambdaQueryWrapper<MiniProgram>()
                .eq(MiniProgram::getPaperId, paperId)
                .eq(MiniProgram::getQrCodeId, id.toString()));
    }

    public Long sharePaper(Long paperId, String route, boolean force) {
        List<Paper> list = list(new LambdaQueryWrapper<Paper>().eq(Paper::getDepRoute, route).eq(Paper::getShareId, paperId).eq(Paper::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (!list.isEmpty()) {
            Long pid = updateSharePaper(paperId, list.get(0).getId(), route, force);
            paperQuestionService.sharePaperQuestions(paperId, pid, route, force);
            return pid;
        }

        Paper paper = baseMapper.getById(paperId);
        if (paper == null) {
            return paperId;
        }

        paper.setCategoryId(categoryService.shareCategory(paper.getCategoryId(), route, force));
        paper.setRuleId(ruleService.shareRule(paper.getRuleId(), route, force));
        paper.setDepRoute(route);
        paper.setShareId(paperId);
        paper.setShareMode("0");
        paper.setId(null);
        baseMapper.insert(paper);
        paperQuestionService.sharePaperQuestions(paperId, paper.getId(), route, force);
        return paper.getId();
    }


    private Long updateSharePaper(Long adminPaperId, Long userPaperId, String route, boolean force) {
        Paper adminPaper = baseMapper.getById(adminPaperId);
        Paper userPaper = baseMapper.getById(userPaperId);
        if (force || !Objects.equals(adminPaper.getUpdateTime(), userPaper.getUpdateTime())) {

            userPaper.setCategoryId(categoryService.shareCategory(adminPaper.getCategoryId(), route, force));
            userPaper.setRuleId(ruleService.shareRule(adminPaper.getRuleId(), route, force));

            userPaper.setAutoExpire(adminPaper.getAutoExpire());
            userPaper.setAutoPush(adminPaper.getAutoPush());
            userPaper.setDelFlag(adminPaper.getDelFlag());
            userPaper.setDisabled(adminPaper.getDisabled());
            userPaper.setDrawType(adminPaper.getDrawType());
            userPaper.setExpiredDuration(adminPaper.getExpiredDuration());
            userPaper.setGroupFactor(adminPaper.getGroupFactor());
            userPaper.setIndustryIds(adminPaper.getIndustryIds());
            userPaper.setIndustryNames(adminPaper.getIndustryNames());
            userPaper.setInputCount(adminPaper.getInputCount());
            userPaper.setIsQuesOrder(adminPaper.getIsQuesOrder());
            userPaper.setIsQuesSort(adminPaper.getIsQuesSort());
            userPaper.setJudgeCount(adminPaper.getJudgeCount());
            userPaper.setLimitTime(adminPaper.getLimitTime());
            userPaper.setMultiCount(adminPaper.getMultiCount());
            userPaper.setName(adminPaper.getName());
            userPaper.setNumExam(adminPaper.getNumExam());
            userPaper.setNumRetries(adminPaper.getNumRetries());
            userPaper.setPostFactor(adminPaper.getPostFactor());
            userPaper.setQualifyScore(adminPaper.getQualifyScore());
            userPaper.setRadioCount(adminPaper.getRadioCount());
            userPaper.setRandomOptions(adminPaper.getRandomOptions());
            userPaper.setShowAnswer(adminPaper.getShowAnswer());
            userPaper.setStartFlag(adminPaper.getStartFlag());
            userPaper.setThinkDuration(adminPaper.getThinkDuration());
            userPaper.setTimeFactor(adminPaper.getTimeFactor());
            userPaper.setType(adminPaper.getType());
            userPaper.setUpdateTime(adminPaper.getUpdateTime());
            userPaper.setWarnDuration(adminPaper.getWarnDuration());

            baseMapper.updateById(userPaper);

        }

        return userPaperId;
    }

}
