package org.jeecg.modules.training.async.report.model;

import java.awt.image.BufferedImage;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DetailExamScl90VO extends DetailExamBaseVO {
    /**
     * 试卷总分(满分)
     */
    protected Double totalPossibleScore;
    
    /**
     * 总体评估指标
     */
    protected Double totalAverageScore; // 总平均分(总分除以总题数)
    protected Integer abnormalFactorsCnt; // 阳性因子数
    protected String abnormalFactorNames; // 阳性因子名称列表 (例:"因子A、因子B")
    protected String overallStatus; // 整体状态
    protected String overallEvaluation; // 整体评价
    
    /**
     * 报告图片数据
     */
    protected BufferedImage companySeal; // 企业公章数据
    
    /**
     * 报告图表数据
     */
    protected List<String[]> radarChartData; // 雷达图数据 格式：[因子名称, 我的平均分, 中国人平均分]
    protected List<String[]> lineChartData;  // 折线图数据 格式：[因子名称, 我的平均分, 中国人平均分]
    
    /**
     * 因子A相关指标
     */
    protected Double factorScore_A; // 因子A得分
    protected Double factorAverageScore_A; // 因子A平均分
    protected String factorStatus_A; // 因子A状态
    protected String factorEvaluation_A; // 因子A评价
    protected BufferedImage horizontalRankingBarChart_A; // 因子A水平分级条形图数据
    protected Double chineseAverageScore_A; // 因子A中国人平均分
    
    /**
     * 因子B相关指标
     */
    protected Double factorScore_B;
    protected Double factorAverageScore_B;
    protected String factorStatus_B;
    protected String factorEvaluation_B;
    protected BufferedImage horizontalRankingBarChart_B;
    protected Double chineseAverageScore_B;
    
    /**
     * 因子C相关指标
     */
    protected Double factorScore_C;
    protected Double factorAverageScore_C;
    protected String factorStatus_C;
    protected String factorEvaluation_C;
    protected BufferedImage horizontalRankingBarChart_C;
    protected Double chineseAverageScore_C;
    
    /**
     * 因子D相关指标
     */
    protected Double factorScore_D;
    protected Double factorAverageScore_D;
    protected String factorStatus_D;
    protected String factorEvaluation_D;
    protected BufferedImage horizontalRankingBarChart_D;
    protected Double chineseAverageScore_D;
    
    /**
     * 因子E相关指标
     */
    protected Double factorScore_E;
    protected Double factorAverageScore_E;
    protected String factorStatus_E;
    protected String factorEvaluation_E;
    protected BufferedImage horizontalRankingBarChart_E;
    protected Double chineseAverageScore_E;
    
    /**
     * 因子F相关指标
     */
    protected Double factorScore_F;
    protected Double factorAverageScore_F;
    protected String factorStatus_F;
    protected String factorEvaluation_F;
    protected BufferedImage horizontalRankingBarChart_F;
    protected Double chineseAverageScore_F;
    
    /**
     * 因子G相关指标
     */
    protected Double factorScore_G;
    protected Double factorAverageScore_G;
    protected String factorStatus_G;
    protected String factorEvaluation_G;
    protected BufferedImage horizontalRankingBarChart_G;
    protected Double chineseAverageScore_G;
    
    /**
     * 因子H相关指标
     */
    protected Double factorScore_H;
    protected Double factorAverageScore_H;
    protected String factorStatus_H;
    protected String factorEvaluation_H;
    protected BufferedImage horizontalRankingBarChart_H;
    protected Double chineseAverageScore_H;
    
    /**
     * 因子I相关指标
     */
    protected Double factorScore_I;
    protected Double factorAverageScore_I;
    protected String factorStatus_I;
    protected String factorEvaluation_I;
    protected BufferedImage horizontalRankingBarChart_I;
    protected Double chineseAverageScore_I;
    
    /**
     * 因子J相关指标
     */
    protected Double factorScore_J;
    protected Double factorAverageScore_J;
    protected String factorStatus_J;
    protected String factorEvaluation_J;
    protected BufferedImage horizontalRankingBarChart_J;
    protected Double chineseAverageScore_J;
}