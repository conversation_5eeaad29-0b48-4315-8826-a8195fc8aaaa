<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.training.mapper.StudyMapper">

  <select id="listPapersPages" resultType="org.jeecg.modules.training.vo.StudyVO">
    SELECT *
    FROM (SELECT s.id,
                 s.category_id,
                 s.name,
                 s.type,
                 s.description,
                 s.paper_id,
                 s.time_factor,
                 s.post_factor,
                 s.group_factor,
                 s.total_time,
                 s.need_read,
                 s.need_attention,
                 s.disabled,
                 c.phase_level,
                 s.need_download,
                 IF(s.type = 2, s.content_html, s.content) AS content,
                 s.del_flag,
                 s.create_by,
                 s.create_time,
                 s.toc_type,
                 s.first_page,
                 c.name                                       category_name,
                 p.name                                       paper_name,
                 s.dep_route,
                 s.industry_ids,
                 s.share_mode,
                 s.feedback_status,
                 s.charge_status,
                 s.charge_type,
                 s.iteam_price
          FROM study s
               LEFT JOIN category c ON s.category_id = c.id
               LEFT JOIN paper p ON p.id = s.paper_id
          WHERE s.del_flag = 0) AS study
      ${ew.customSqlSegment}
  </select>

  <select id="findStudies" resultType="org.jeecg.modules.training.vo.StudyVO">
      select *
      from (
          select s.id,
                 s.category_id,
                 s.name,
                 s.type,
                 s.description,
                 s.paper_id,
                 s.time_factor,
                 s.post_factor,
                 s.size,
                 s.need_download,
                 s.group_factor,
                 s.total_time,
                 s.disabled,
                 s.update_time,
                 s.stream_url,
                 s.share_id,
                 if(sus1.id is null, false, true) as is_read,
                 if(sus2.id is null, if(sus3.id is null, 0, sus3.state), sus2.state) as is_attention,
                 sln.num as done_times,
                 s.del_flag,
                 s.create_by,
                 s.create_time,
                 c.name as category_name,
                 p.name as paper_name,
                 if(#{posts} regexp concat(',', replace(s.need_read, ',', ',|,'), ','), 'true', 'false') as need_read,
                 if(#{posts} regexp concat(',', replace(s.need_attention, ',', ',|,'), ','), 'true', 'false') as need_attention,
                 c.app_menu,
                 s.dep_route,
                 s.feedback_status,
                 s.charge_status,
                 -- 添加所需会员等级字段
                 CASE
                     WHEN s.charge_status = 0 THEN 0
                     ELSE s.charge_type
                 END as charge_type
          from study s
                   left join category c on s.category_id = c.id
                   left join paper p on p.id = s.paper_id
                   LEFT JOIN study_log_num sln on sln.study_id = s.id and sln.user_id = #{userId}
                   left join study_user_state sus1 on sus1.study_id = s.id and sus1.user_id = #{userId} and sus1.state = 1
                   left join study_user_state sus2 on sus2.study_id = s.id and sus2.user_id = #{userId} and sus2.state = 2
                   left join study_user_state sus3 on sus3.study_id = s.id and sus3.user_id = #{userId} and sus3.state = 3
                   -- 关联iteams_display_order表获取显示顺序
                   left join iteams_display_order ido on ido.member_package_lev = #{memberLevel}
                       and ido.iteam_package_lev = CASE
                           WHEN s.charge_status = 0 THEN 0
                           ELSE s.charge_type
                       END
          where s.del_flag = 0
            <if test="categoryId != null">
                and s.category_id = #{categoryId}
            </if>
            <if test="menu != null and menu != ''">
                and c.app_menu = #{menu}
            </if>
            and s.disabled = false
            and (
                (
                    (s.time_factor is null or s.time_factor = '' or s.time_factor = '0' or s.time_factor = ',任意,' or
                     find_in_set(#{boardTime}, s.time_factor))
                    and
                    (s.post_factor is null or s.post_factor = '' or s.post_factor = '0' or s.post_factor = '任意' or
                     #{posts} regexp concat(',', replace(s.post_factor, ',', ',|,'), ','))
                )
                <if test="groupInfo != null and groupInfo != ''">
                    or (s.group_factor is not null and s.group_factor &lt;&gt; '' and
                        #{groupInfo} regexp concat(',', replace(s.group_factor, ',', ',|,'), ','))
                </if>
            )
            -- 企业会员制权限过滤逻辑
            <if test="memberType != null and memberType == 1">
                and (
                    -- 免费资料，所有人都可以访问
                    s.charge_status = 0
                    -- 或者用户套餐等级满足资料所需等级
                    or (s.charge_status = 1 and #{memberLevel} >= COALESCE(s.charge_type, 1))
                    -- 或者已单独购买（企业会员模式检查depart_ordered_iteams表）
                    or exists (
                        select 1 from depart_ordered_iteams doi
                        where doi.depart_id = #{departId}
                          and doi.categorie_type = 3
                          and doi.iteam_id = s.share_id
                          and s.share_id is not null
                    )
                )
            </if>
            -- 船员会员制权限过滤逻辑
            <if test="memberType != null and memberType == 0">
                and (
                    -- 免费资料，所有人都可以访问
                    s.charge_status = 0
                    -- 或者用户套餐等级满足资料所需等级
                    or (s.charge_status = 1 and #{memberLevel} >= COALESCE(s.charge_type, 1))
                    -- 或者已单独购买（船员会员模式检查trainee_ordered_iteams表）
                    or exists (
                        select 1 from trainee_ordered_iteams toi
                        where toi.trainee_id = #{userId}
                          and toi.categorie_type = 3
                          and toi.iteam_id = s.share_id
                          and s.share_id is not null
                    )
                )
            </if>
          -- 按照iteams_display_order表中定义的顺序排序，然后按创建时间倒序
          order by COALESCE(ido.sequence, 999) asc, s.create_time desc
      ) as study
      ${ew.customSqlSegment}
  </select>

  <select id="getNeedReadNumber" resultType="java.lang.Long">
    select count(1)
    from study s
    where
    s.del_flag = 0
    and s.category_id = #{categoryId}
    and s.disabled = false
    and (
    (
    (s.time_factor is null or s.time_factor = '' or s.time_factor = '0' or s.time_factor = ',任意,' or find_in_set(#{boardTime}, s.time_factor))
    and
    (s.post_factor is null or s.post_factor = '' or s.post_factor = '0' or s.post_factor = '任意' or
    #{post} regexp concat(',' , replace(s.post_factor, ',', ',|,'),',')
    )
    )
    <if test="groupInfo!=null and groupInfo!= ''">
      or (s.group_factor is not null and s.group_factor &lt;&gt; '' and
      #{groupInfo} regexp concat(',' , replace(s.group_factor, ',', ',|,'), ',')
      )
    </if>
    )
    and s.need_read is not null
    and
    #{post} regexp concat(',' , replace(s.need_read, ',', ',|,'),',')
  </select>
  <select id="getNeedAttentionNumber" resultType="java.lang.Long">
    select count(1)
    from study s LEFT JOIN category c ON s.category_id = c.id
    where
    s.del_flag = 0
    and c.route LIKE #{categoryRoute}
    and s.disabled = false
    and (
    (
    (s.time_factor is null or s.time_factor = '' or s.time_factor = '0' or s.time_factor = ',任意,' or find_in_set(#{boardTime}, s.time_factor))
    and
    (s.post_factor is null or s.post_factor = '' or s.post_factor = '0' or s.post_factor = '任意' or
    #{post} regexp concat(',' , replace(s.post_factor, ',', ',|,'),',')
    )
    )
    <if test="groupInfo!=null and groupInfo!= ''">
      or (s.group_factor is not null and s.group_factor &lt;&gt; '' and
      #{groupInfo} regexp concat(',' , replace(s.group_factor, ',', ',|,'), ',')
      )
    </if>
    )
    and s.need_attention is not null
    and
    #{post} regexp concat(',' , replace(s.need_attention, ',', ',|,'),',')
  </select>
  <select id="getById" resultType="org.jeecg.modules.training.entity.Study">
    SELECT *
    FROM study
    WHERE id = ${id};
  </select>

  <select id="listUnfinishedStudyUserPage" resultType="org.jeecg.modules.training.vo.UserStudy">
    SELECT *
    FROM (SELECT s.id,
                 s.name      AS study_name,
                 trainee.realname,
                 trainee.post,
                 phase.name  AS phase_name,
                 team.team_name,
                 team.route  AS team_route,
                 s.industry_ids,
                 s.dep_route AS study_dep_route,
                 s.create_time
          FROM study s
               LEFT JOIN phase_item pi ON pi.data_id = s.id
               LEFT JOIN phase ON phase.id = pi.phase_id
               LEFT JOIN user_phase_item upi ON upi.phase_item_id = pi.id
               LEFT JOIN trainee ON trainee.id = upi.user_id
               LEFT JOIN team ON team.id = trainee.team_id
          WHERE s.del_flag = 0
            AND pi.del_flag = 0
            AND trainee.del_flag = 0
            AND team.del_flag = 0
            AND upi.status = 0) AS study
      ${ew.customSqlSegment}
  </select>
</mapper>
