package org.jeecg.modules.training.util;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/05/08/18:17
 */
@Getter
@AllArgsConstructor
public enum UtilsEnums {

    CHAPTER_TITLE_REGEX("^\\S\\d章[\\s\\S]+$", "章节正则表达式"),
    FIRST_TITLE_REGEX("^\\d{1,2}.\\d[\\s\\S]+$", "章节正则表达式"),
    TOC_REGEX("^.*\\.{3,}\\d+$", "目录正则表达式");
    //[.]{3,}\d+$

    private String code;

    private String desc;
}