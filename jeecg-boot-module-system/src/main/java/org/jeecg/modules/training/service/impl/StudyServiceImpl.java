package org.jeecg.modules.training.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.GetResponse;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.AnalyzeResponse;
import co.elastic.clients.elasticsearch.indices.analyze.AnalyzeToken;
import co.elastic.clients.elasticsearch.nodes.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.StatusLine;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.ResponseException;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.mapper.StudyMapper;
import org.jeecg.modules.training.service.ICategoryService;
import org.jeecg.modules.training.service.IPaperService;
import org.jeecg.modules.training.service.IStudyService;
import org.jeecg.modules.training.vo.FileForESDTO;
import org.jeecg.modules.training.vo.StudyPage;
import org.jeecg.modules.training.vo.StudyVO;
import org.jeecg.modules.training.vo.UserStudy;
import org.jeecg.modules.transform.service.ISyncStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.ConnectException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 学习表
 * @Author: jeecg-boot
 * @Date: 2022-09-02
 * @Version: V1.0
 */
@Service
@Slf4j
public class StudyServiceImpl extends ServiceImpl<StudyMapper, Study> implements IStudyService {

    @Autowired
    @Lazy
    private ICategoryService categoryService;
    @Autowired
    @Lazy
    private IPaperService paperService;

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Value(value = "${jeecg.path.dataRoot}")
    private String dataRoot;

    // ES中存放学习资料的索引名称
    @Value(value = "${jeecg.elasticsearch.indexName}")
    private String indexName;

    @Autowired
    private ISyncStateService syncStateService;

    @Override
    public IPage<StudyVO> listPages(Page<StudyVO> page, QueryWrapper<StudyVO> queryWrapper) {
        return baseMapper.listPapersPages(page, queryWrapper);
    }

    /**
     * APP端查询学习资料列表
     */
    @Override
    public IPage<StudyVO> findStudies(StudyPage<StudyVO> studyPage, Long categoryId, String userId, String boardTime, String post, String groupInfo, String menu, LambdaQueryWrapper<StudyVO> lambdaQueryWrapper) {
        return baseMapper.findStudies(studyPage, categoryId, userId, boardTime, post, groupInfo, menu, null, null, null, lambdaQueryWrapper);
    }

    /**
     * APP端查询学习资料列表（带权限过滤和排序）
     */
    @Override
    public IPage<StudyVO> findStudies(StudyPage<StudyVO> studyPage, Long categoryId, String userId, String boardTime, String post, String groupInfo, String menu, String departId, Integer memberType, Short memberLevel, LambdaQueryWrapper<StudyVO> lambdaQueryWrapper) {
        return baseMapper.findStudies(studyPage, categoryId, userId, boardTime, post, groupInfo, menu, departId, memberType, memberLevel, lambdaQueryWrapper);
    }

    /**
     * 查询当前用户需要阅读的学习资料数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    @Override
    public Long getNeedReadNumber(String userId, String boardTime, String post, String groupInfo, Category category) {
        //用户有组信息查询特定组
        //List<String> groupInfoList = StringUtils.isEmpty(groupInfo) ? null : Arrays.asList(groupInfo.split(","));
        return baseMapper.getNeedReadNumber(userId, category.getId(), boardTime, post, groupInfo);
    }

    /**
     * 获取当前用户需要关注学习资料的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    @Override
    public Long getNeedAttentionNumber(String userId, String boardTime, String post, String groupInfo, Category category) {
        //用户有组信息查询特定组
        //List<String> groupInfoList = StringUtils.isEmpty(groupInfo) ? null : Arrays.asList(groupInfo.split(","));
        return baseMapper.getNeedAttentionNumber(userId, category.getRoute() + "%", boardTime, post, groupInfo);
    }

    @Override
    public List<Study> getStudyByName(String studyName, String menu) {
        return null;
    }

    @Override
    public IPage<UserStudy> listUnfinishedStudyUserPage(Page<UserStudy> page, QueryWrapper<StudyVO> queryWrapper) {
        return baseMapper.listUnfinishedStudyUserPage(page, queryWrapper);
    }

    public Long shareStudy(Long studyId, String route, boolean force) {
        List<Study> list = list(new LambdaQueryWrapper<Study>().eq(Study::getDepRoute, route).eq(Study::getShareId, studyId).eq(Study::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (!list.isEmpty())
            return updateShareStudy(studyId, list.get(0).getId(), route, force);

        Study study = baseMapper.getById(studyId);
        if (study == null) {
            return studyId;
        }

        study.setCategoryId(categoryService.shareCategory(study.getCategoryId(), route, force));
        study.setDepRoute(route);
        study.setShareMode("0");
        study.setShareId(studyId);
        study.setId(null);
        baseMapper.insert(study);
        return study.getId();
    }

    private Long updateShareStudy(Long adminStudyId, Long userStudyId, String route, boolean force) {
        Study adminStudy = baseMapper.getById(adminStudyId);
        Study userStudy = baseMapper.getById(userStudyId);
        if (force || !Objects.equals(adminStudy.getUpdateTime(), userStudy.getUpdateTime())) {

            userStudy.setCategoryId(categoryService.shareCategory(adminStudy.getCategoryId(), route, force));

            if (adminStudy.getPaperId() != null)
                userStudy.setPaperId(paperService.sharePaper(adminStudy.getPaperId(), route, force));


            userStudy.setCompressDpi(adminStudy.getCompressDpi());
            userStudy.setContent(adminStudy.getContent());
            userStudy.setContent(adminStudy.getContent());
            userStudy.setContentHtml(adminStudy.getContentHtml());
            userStudy.setDelFlag(adminStudy.getDelFlag());
            userStudy.setDescription(adminStudy.getDescription());
            userStudy.setDisabled(adminStudy.getDisabled());
            userStudy.setFeedbackStatus(adminStudy.getFeedbackStatus());
            userStudy.setFilePath(adminStudy.getFilePath());
            userStudy.setFirstPage(adminStudy.getFirstPage());
            userStudy.setGroupFactor(adminStudy.getGroupFactor());
            userStudy.setIndustryIds(adminStudy.getIndustryIds());
            userStudy.setName(adminStudy.getName());
            userStudy.setNeedAttention(adminStudy.getNeedAttention());
            userStudy.setNeedDownload(adminStudy.getNeedDownload());
            userStudy.setNeedRead(adminStudy.getNeedRead());
            userStudy.setPostFactor(adminStudy.getPostFactor());
            userStudy.setSize(adminStudy.getSize());
            userStudy.setStreamUrl(adminStudy.getStreamUrl());
            userStudy.setTimeFactor(adminStudy.getTimeFactor());
            userStudy.setTocType(adminStudy.getTocType());
            userStudy.setTotalTime(adminStudy.getTotalTime());
            userStudy.setType(adminStudy.getType());

            userStudy.setUpdateTime(adminStudy.getUpdateTime());

            baseMapper.updateById(userStudy);
        }
        return userStudyId;
    }

    // 判断ES中是否存在指定id对应的数据 存在返回true，不存在返回false
    @Override
    public Boolean existsById(String studyId) {
        try {
            GetResponse<FileForESDTO> response = elasticsearchClient.get(g -> g
                            .index(indexName)
                            .id(studyId),
                    FileForESDTO.class);
            return response.found();
        } catch (ConnectException e) {
            log.error("系统无法连接到ElasticSearch服务，ES可能没有正常启动", e);
            throw new JeecgBootException("系统无法连接到ElasticSearch服务，ES可能没有正常启动");
        } catch (Exception e) {
            log.error("查询学习资料id = {}在ES是否存在出错", studyId, e);
            return null;
        }
    }

    // 把单个学习资料存放到ES的索引库中
    @Override
    public void addStudyToIndex(Study study) {
        // 构造上传对象
        FileForESDTO fileForESDTO;
        try {
            fileForESDTO = createESFile(study);
        } catch (Exception e) {
            log.error("构造上传到ES的对象时出错", e);
            return;
        }

        // 判断JVM内存是否超过预警值，超过则跳过该学习资料
        if (isJvmMemoryUsageHigh()) {
            log.info("Elasticsearch导致JVM内存持续占用超过预警值，跳过该学习资料");
            return;
        }

        // 上传到ES
        createIndex(fileForESDTO);

    }

    /**
     *
     * @return JVM内存是否超过85% true：是；false：否
     */
    private boolean isJvmMemoryUsageHigh() {

        int retries = 1;
        // 最大重试次数
        int maxRetries = 9;
        // 最大预警内存使用率
        int maxMemoryUsage = 85;

        while (retries < maxRetries) {
            if (getJvmMemoryUsage() >= maxMemoryUsage) {
                int sleepTime = 1 << retries; // 2^n，n需在 0~30 范围内
                log.info("ElasticSearch导致JVM内存占用超过{}%，程序第{}次暂停{}秒", maxMemoryUsage, retries, sleepTime);
                try {
                    Thread.sleep(sleepTime * 1000L);
                    if (getJvmMemoryUsage() <= maxMemoryUsage) {
                        log.info("JVM内存占用没有超过{}%，系统恢复运行", maxMemoryUsage);
                        return false;
                    }

                    retries++;
                } catch (InterruptedException e) {
                    log.error("ElasticSearch导致JVM内存占用超过{}%，系统尝试休眠时失败", maxMemoryUsage, e);
                    return true;
                }
            } else { // JVM内存没有超过最大值
                return false;
            }
        }

        if (retries == maxRetries) {
            log.error("ElasticSearch导致JVM内存占用超过{}%，程序已经暂停{}次后仍然没有恢复运行，跳过该学习资料", maxMemoryUsage, retries);
            return true;
        }
        return false;
    }

    // 获取JVM内存占用百分比
    private double getJvmMemoryUsage() {
        // 构建请求
        NodesStatsRequest request = NodesStatsRequest.of(b -> b
                .metric("jvm")  // 只请求jvm指标
        );

        // 执行请求
        NodesStatsResponse response;
        try {
            response = elasticsearchClient.nodes().stats(request);
        } catch (IOException e) {
            log.error("操作失败，获取ES的JVM内存信息失败，JVM内存占用暂时返回0.00", e);
            return 0.00;
        }

        // 获取usedInBytes和maxInBytes计算JVM内存占用
        int jvmMemoryUsage = 0;
        if (response.nodes() != null) {
            for (Map.Entry<String, Stats> entry : response.nodes().entrySet()) {
                Jvm jvm = entry.getValue().jvm();
                if (jvm != null && jvm.mem() != null && jvm.mem().pools() != null) {
                    Pool old = jvm.mem().pools().get("old");
                    if (old != null) {
                        Long usedInBytes = old.usedInBytes();
                        Long maxInBytes = old.maxInBytes();
                        if (usedInBytes != null && maxInBytes != null) {
                            // 四舍五入保留两位小数
                            jvmMemoryUsage = (int) ((Double.parseDouble(NumberUtil.div(usedInBytes, maxInBytes, 2).toString())) * 100);
                            // 默认只有一个节点，所以跳出循环
                            break;
                        }
                    }
                }
            }
        }
        log.info("当前JVM内存占用百分比 = {}%", jvmMemoryUsage);
        return jvmMemoryUsage;

    }

    // 通过文本抽取管道上传学习资料到ElasticSearch指定索引库
    private void createIndex(FileForESDTO fileForESDTO) {

        int retries = 1;
        int maxRetries = 9;

        while (retries < maxRetries) {
            try {
                elasticsearchClient.index(i -> i
                        .index(indexName)// 索引名字
                        .id(fileForESDTO.getId())
                        .pipeline("attachment")
                        .document(fileForESDTO)
                );
                log.info("学习资料id = {} 上传到ES的索引库成功", fileForESDTO.getId());
                break; // 成功则退出循环
            } catch (ResponseException e) {
                Response response = e.getResponse();
                if (response != null) {
                    StatusLine statusLine = response.getStatusLine();
                    if (statusLine != null) {
                        int statusCode = statusLine.getStatusCode();
                        if (statusCode == 429 || statusCode == 503) { // 429：断路器错误，拒绝请求

                            log.error("索引学习资料id = {}时，ES返回了429，拒绝了请求", fileForESDTO.getId());
                            int sleepTime = 1 << retries; // 秒
                            try {
                                log.info("系统第{}次尝试暂停{}秒来解决429报错", retries, sleepTime);
                                Thread.sleep(sleepTime * 1000L);
                            } catch (InterruptedException ex) {
                                log.error("系统尝试暂停{}秒解决429报错时无法暂停，跳过学习资料id = {}", sleepTime, fileForESDTO.getId(), ex);
                                return;
                            }

                            if (isJvmMemoryUsageHigh()) {
                                log.info("Elasticsearch导致JVM内存持续占用超过85%，跳过学习资料id = {}", fileForESDTO.getId());
                                return;
                            }

                            retries++;
                        } else {
                            log.error("索引学习资料id = {}到ES时出现了非429错误，暂时跳过", fileForESDTO.getId(), e);
                            return;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("索引学习资料id = {}到ES时出现了非429错误，暂时跳过", fileForESDTO.getId(), e);
                return;
            }
        }

        if (retries == maxRetries) {
            log.error("程序已经尝试暂停{}次解决429报错，但都失败了（学习资料id = {}），暂时跳过", retries - 1, fileForESDTO.getId());
        }
    }

    // 构造 ES 上传文件对象
    private FileForESDTO createESFile(Study study) {
        FileForESDTO fileForESDTO = new FileForESDTO();

        fileForESDTO.setId(study.getId().toString());
        fileForESDTO.setName(study.getName());
        fileForESDTO.setDescription(study.getDescription());
        fileForESDTO.setType(study.getType());

        // 设置 content
        if (fileForESDTO.getType() == 1) { // 视频为空
            fileForESDTO.setContent("");
        } else { // PDF和HTML为文件的Base64编码
            String base64Content = getBase64Content(String.valueOf(study.getId()), study.getContent());
            fileForESDTO.setContent(base64Content);
        }

        return fileForESDTO;
    }


    // 获取学习资料的Base64编码字符 支持PDF、WORD、HTML等非文本文件
    private String getBase64Content(String studyId, String content) {
        // 学习资料存储路径
        String filePath = dataRoot + File.separator + content;
        // 判断文件是否存在
        if (!FileUtil.exist(filePath)) { // 不存在 抛出异常
            log.error("学习资料id = {}对应的磁盘文件不存在", studyId);
            return "";
        }

        // 根据 filePath 存储路径，获取文件二进制数组
        byte[] fileBytes = FileUtil.readBytes(filePath);
        // fileBytes 转为 Base64编码的字符串
        String fileBase64 = Base64.encode(fileBytes);

        if (fileBase64 == null || fileBase64.isEmpty()) { // 文件内容为空或无法访问进入
            log.info("学习资料id = {}的Base64编码为空，忽略内容索引", studyId);
            return "";
        } else {
            int size = fileBase64.getBytes().length / (1024 * 1024);
            if (size > 100) {
                log.info("学习资料id = {}的Base64编码大于100M，忽略内容索引", studyId);
                return "";
            }
        }

        return fileBase64;
    }

    /**
     * 把当前库中所有学习资料存放到ES的索引库中
     *
     * @param force 当ES中数据存在时，是否强制更新 true：删除旧的，创建新的；false：不更新
     */
    @Override
    public void fixStudyIndex(Boolean force) {
        // 获取所有类型的学习资料
        LambdaQueryWrapper<Study> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Study::getDelFlag, 0);
        List<Study> studyList = baseMapper.selectList(queryWrapper);

        studyList.forEach(study -> {

            // 判断ES中是否存在
            Boolean isExists = existsById(study.getId().toString());
            if (isExists == null) { // 查询学习资料在ES是否存在出错，跳过该学习资料
                return;
            }

            if (isExists) { // 存在
                if (force) { // 强制更新
                    // 删除
                    deleteStudyToIndex(study.getId().toString());
                    // 添加
                    addStudyToIndex(study);
                }
            } else {
                addStudyToIndex(study);
            }
        });
    }

    // 删除磁盘中不存在的学习资料记录
    @Override
    public void deleteNotExistStudy() {
        // 获取所有的学习资料
        List<Study> studyList = baseMapper.selectList(null);

        studyList.forEach(study -> {
            // 获取学习资料路径
            String filePath = dataRoot + File.separator + study.getContent();
            // 判断文件是否存在
            if (!FileUtil.exist(filePath)) {
                // 不存在 删除学习资料记录
                baseMapper.deleteById(study.getId());
                syncStateService.resetSyncState(CommonConstant.STUDY, String.valueOf(study.getId()));
            }
        });
    }

    // 创建普通match查询
    private Query createQuery(String field, String content) {
        return MatchQuery.of(m -> m
                .field(field)
                .query(content)
        )._toQuery();
    }

    /**
     * 按学习资料名称、描述、内容进行Elasticsearch搜索
     * 搜索规则：满足三者之一就会被搜索
     */
    @Override
    public List<String> fullTextSearch(String content) {
        try {

            // 请求Elasticsearch进行搜索，满足三者之一就会被搜索
            SearchResponse<JSONObject> searchResponse = elasticsearchClient.search(s -> s
                    .index(indexName)
                    .storedFields("_id")
                    .size(2000)
                    .query(q -> q
                            .bool(b -> b
                                    .should(createQuery("name", content))
                                    .should(createQuery("description", content))
                                    .should(createQuery("attachment.content", content)))), JSONObject.class);
            // 解析搜索结果
            if (searchResponse.hits().total() != null) {
                // 获取学习资料id 文档id即为学习资料id
                return searchResponse.hits().hits().stream()
                        .map(Hit::id)
                        .collect(Collectors.toList());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    // // 更新ES中的文档 updateContent表示是否修改文件文本内容
    @Override
    public void updateStudyToIndex(StudyVO studyVO, Boolean updateContent) {

        if (updateContent) { // 文件内容被修改了，则删除文档，新建
            deleteStudyToIndex(studyVO.getId().toString());
            addStudyToIndex(studyVO);
            log.info("修改ES中的数据成功");
        } else { // 文件内容没有修改
            // 设置要修改的值
            FileForESDTO fileForESDTO = new FileForESDTO();
            fileForESDTO.setId(String.valueOf(studyVO.getId()));
            fileForESDTO.setName(studyVO.getName());
            fileForESDTO.setType(studyVO.getType());
            fileForESDTO.setDescription(studyVO.getDescription());

            // 修改ES索引库中的文档
            try {
                elasticsearchClient.update(u -> u
                                .index(indexName)
                                .id(fileForESDTO.getId())
                                .doc(fileForESDTO),
                        FileForESDTO.class);
            } catch (IOException e) {
                log.error("尝试修改ES中的数据时出错", e);
            }
        }
    }

    // 删除ES中指定的文档
    @Override
    public void deleteStudyToIndex(String studyId) {
        try {
            elasticsearchClient.delete(d -> d
                    .index(indexName)
                    .id(studyId));
        } catch (Exception e) {
            log.error("尝试删除ES中的学习资料时出错", e);
        }
    }

    // 获取搜索内容的分词结果
    @Override
    public List<String> getSearchTokens(String content) {

        try {
            // 向ES发起请求获取分词结果
            AnalyzeResponse analyzeResponse = elasticsearchClient.indices().analyze(a -> a
                    .index(indexName)
                    .analyzer("ik_smart")
                    .text(content));
            // 解析结果并返回
            return analyzeResponse.tokens().stream()
                    .map(AnalyzeToken::token)
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("尝试获取搜索词时出错", e);
            return null;
        }
    }
}
