package org.jeecg.modules.training.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.vo.StudyPage;
import org.jeecg.modules.training.vo.StudyVO;
import org.jeecg.modules.training.vo.UserStudy;

/**
 * @Description: 学习表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
public interface StudyMapper extends BaseMapper<Study> {

    IPage<StudyVO> listPapersPages(Page<StudyVO> page, @Param("ew") QueryWrapper<StudyVO> queryWrapper);

    IPage<StudyVO> findStudies(
            StudyPage<StudyVO> studyPage,
            @Param("categoryId") Long categoryId,
            @Param("userId") String userId,
            @Param("boardTime") String boardTime,
            @Param("posts") String posts,
            @Param("groupInfo") String groupInfo,
            @Param("menu") String menu,
            @Param("ew") LambdaQueryWrapper<StudyVO> lambdaQueryWrapper);

    /**
     * 查询当前用户需要阅读的学习资料数量
     *
     * @param userId
     * @param categoryId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @return
     */
    Long getNeedReadNumber(@Param("userId") String userId, @Param("categoryId") Long categoryId, @Param("boardTime") String boardTime, @Param("post") String post, @Param("groupInfo") String groupInfo);

    /**
     * @param userId
     * @param categoryId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @return
     */
    Long getNeedAttentionNumber(@Param("userId") String userId, @Param("categoryRoute") String categoryRoute, @Param("boardTime") String boardTime, @Param("post") String post, @Param("groupInfo") String groupInfo);

    Study getById(@Param("id") Long studyId);

    IPage<UserStudy> listUnfinishedStudyUserPage(Page<UserStudy> page, @Param("ew") QueryWrapper<StudyVO> queryWrapper);
}
