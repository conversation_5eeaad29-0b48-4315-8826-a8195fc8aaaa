package org.jeecg.modules.training.controller.app;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.training.entity.Phase;
import org.jeecg.modules.training.entity.Trainee;
import org.jeecg.modules.training.entity.UserPhase;
import org.jeecg.modules.training.service.IPhaseService;
import org.jeecg.modules.training.service.IUserPhaseItemService;
import org.jeecg.modules.training.service.IUserPhaseService;
import org.jeecg.modules.training.service.TraineeService;
import org.jeecg.modules.training.util.ExamUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;

/**
 * @Description: 用户成长阶段
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "船员培训阶段")
@RestController
@RequestMapping("/app/userPhase")
public class AppUserPhaseController extends JeecgController<UserPhase, IUserPhaseService> {

    @Autowired
    private IPhaseService phaseService;

    @Autowired
    private IUserPhaseItemService userPhaseItemService;

    @Autowired
    private TraineeService traineeService;


    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping(value = "/getMyPhases")
    public Result<?> getMyPhases() {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        Trainee trainee = traineeService.getById(loginUser.getId());

        //上船时间
        String boardTime = ExamUtils.getUserBoardTimeName(trainee);
        //职务
        String post = "," + trainee.getPost() + ",";

        QueryWrapper<UserPhase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", loginUser.getId());
        queryWrapper.eq("dep_route", loginUser.getDepRoute());

        List<UserPhase> list = service.list(queryWrapper);

        list.forEach(p -> {
            Phase phase = phaseService.getById(p.getPhaseId());
            p.setId(p.getPhaseId());
            p.setPhaseId(null);
            p.setName(phase.getName());
            p.setAutoPush(phase.getAutoPush());
            p.setTodo(userPhaseItemService.getUserTodoCount(loginUser.getId(), phase.getLevel(), boardTime, post, loginUser.getDepRoute()));
            String icon = phase.getIcon() != null ? phase.getIcon() : "";
            p.setIcon(icon);
        });

        list.sort(Comparator.comparingInt(UserPhase::getLevel));

        return Result.OK(list);
    }

    // 获取用户指定阶段下的待办数量
    @GetMapping(value = "/getMyTodoCount")
    public Result<?> getMyTodoCount(@RequestParam String phaseId) {

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new JeecgBootException("用户token过期，请重新登陆！");
        }

        Trainee trainee = traineeService.getById(loginUser.getId());
        if (trainee == null) {
            log.error("系统未查询到用户{}的数据", loginUser.getId());
            return Result.OK(null);
        }

        //上船时间
        String boardTime = ExamUtils.getUserBoardTimeName(trainee);
        //职务
        String post = "," + trainee.getPost() + ",";

        Phase phase = phaseService.getById(phaseId);
        if (phase == null) {
            log.error("系统未查询到指定阶段{}的数据", phaseId);
            return Result.OK(null);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("toDoCount", userPhaseItemService.getUserTodoCount(loginUser.getId(), phase.getLevel(), boardTime, post, loginUser.getDepRoute()));
        return Result.OK(jsonObject);
    }
}
