package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.training.entity.Category;
import org.jeecg.modules.training.entity.Study;
import org.jeecg.modules.training.vo.StudyPage;
import org.jeecg.modules.training.vo.StudyVO;
import org.jeecg.modules.training.vo.UserStudy;

import java.util.List;

/**
 * @Description: 学习表
 * @Author: hzk
 * @Date: 2022-09-02
 * @Version: V1.0
 */
public interface IStudyService extends IService<Study> {

    /**
     * 分页列表查询
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<StudyVO> listPages(Page<StudyVO> page, QueryWrapper<StudyVO> queryWrapper);

    /**
     * app查询列表
     *
     * @param id
     * @param time
     * @param categoryId
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param studyName
     * @param menu
     * @return
     */
    IPage<StudyVO> findStudies(StudyPage<StudyVO> studyPage, Long categoryId, String userId, String boardTime, String post, String groupInfo, String menu, LambdaQueryWrapper<StudyVO> lambdaQueryWrapper);


    /**
     * 获取当前用户需要阅读学习资料的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    Long getNeedReadNumber(String userId, String boardTime, String post, String groupInfo, Category category);

    /**
     * 获取当前用户需要关注学习资料的数量
     *
     * @param userId
     * @param boardTime
     * @param post
     * @param groupInfo
     * @param category
     * @return
     */
    Long getNeedAttentionNumber(String userId, String boardTime, String post, String groupInfo, Category category);

    List<Study> getStudyByName(String studyName, String menu);

    Long shareStudy(Long studyId, String route, boolean force);

    IPage<UserStudy> listUnfinishedStudyUserPage(Page<UserStudy> page, QueryWrapper<StudyVO> queryWrapper);

    // 判断ES中是否存在指定id对应的数据 存在返回true，不存在返回false
    Boolean existsById(String studyId);

    // 把单个学习资料存放到ES的索引库中
    void addStudyToIndex(Study study);

    /**
     * 把当前库中所有学习资料存放到ES的索引库中
     * @param force 当ES中数据存在时，是否强制更新 true：删除旧的，创建新的；false：不更新
     */
    void fixStudyIndex(Boolean force);

    // 删除磁盘中不存在的学习资料记录
    void deleteNotExistStudy();

    // 按学习资料内容Elasticsearch搜索
    List<String> fullTextSearch(String content);

    // 更新ES中的文档 updateContent表示是否修改文件文本内容
    void updateStudyToIndex(StudyVO study, Boolean updateContent);

    // 删除ES中指定的文档
    void deleteStudyToIndex(String studyId);

    // 获取搜索内容的分词结果
    List<String> getSearchTokens(String content);
}
