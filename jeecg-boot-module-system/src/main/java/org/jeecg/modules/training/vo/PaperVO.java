package org.jeecg.modules.training.vo;

import lombok.Data;
import org.jeecg.modules.training.entity.Paper;

/**
 * 试卷视图模型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2022年05月07日 18:54
 */
@Data
public class PaperVO extends Paper {
    /**
     * 用户试卷ID
     */
    private Long userPaperId;
    /**
     * 总考试次数（UserPaper）
     */
    private Integer totalTimes;
    /**
     * 重试次数（UserPaper）
     */
    private Integer retryTimes;
    /**
     * 已考次数（UserPaper）
     */
    private Integer doneTimes;
    /**
     * 合格次数（UserPaper）
     */
    private Integer passedTimes;
    /**
     * 不合格次数
     */
    private Integer failedTimes;
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 经过推送调整后的考试名称（UserPaper）
     */
    private String paperName;

    private Boolean beforeAnswering;

    private Boolean openCamera;

    private String memo;

    /**
     * 显示顺序（来自iteams_display_order表）
     */
    private Integer displayOrder;
}
