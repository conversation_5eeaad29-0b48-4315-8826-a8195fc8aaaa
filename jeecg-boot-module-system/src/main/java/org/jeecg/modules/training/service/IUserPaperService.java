package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
public interface IUserPaperService extends IService<UserPaper> {

    /**
     * APP端查询试卷列表（统一方法 - 支持基础版本和增强版本）
     *
     * <p><strong>使用说明：</strong></p>
     * <ul>
     *   <li><strong>基础版本：</strong>当 departId、memberType、memberLevel 为 null 时，执行基础查询</li>
     *   <li><strong>增强版本：</strong>当 departId、memberType、memberLevel 都有值时，执行带权限过滤和排序的查询</li>
     * </ul>
     *
     * <p><strong>功能特性：</strong></p>
     * <ul>
     *   <li>根据 iteams_display_order 表进行自定义排序（仅增强版本）</li>
     *   <li>基于会员等级进行权限过滤（仅增强版本）</li>
     *   <li>支持企业会员制和船员会员制两种模式（仅增强版本）</li>
     *   <li>在SQL层面实现权限检查，提升查询效率</li>
     *   <li>向后兼容：支持原有的两参数调用方式</li>
     * </ul>
     *
     * <p><strong>设计优势：</strong></p>
     * <ol>
     *   <li><strong>代码统一：</strong>合并两个方法，减少代码重复，便于维护</li>
     *   <li><strong>智能切换：</strong>根据参数是否为null自动选择执行逻辑</li>
     *   <li><strong>向后兼容：</strong>支持原有调用方式，不破坏现有代码</li>
     *   <li><strong>SQL优化：</strong>在单个SQL中实现所有逻辑，性能更优</li>
     * </ol>
     *
     * @param userId 用户ID（船员ID）
     * @param categoryId 分类ID
     * @param departId 部门ID（可选：null=基础版本，有值=增强版本）
     * @param memberType 会员类型（可选：null=基础版本，0=船员会员模式，1=企业会员模式）
     * @param memberLevel 会员等级（可选：null=基础版本，0=无有效套餐，1=基础版，2=标准版，3=尊享版）
     * @return 试卷列表
     */
    List<PaperVO> findPapers(String userId, Long categoryId, String departId, Integer memberType, Short memberLevel);

    /**
     * 获取试卷（向后兼容方法）
     *
     * <p>此方法为向后兼容而保留，内部调用统一的 findPapers 方法。
     * 建议新代码直接使用 findPapers(userId, categoryId, null, null, null)。</p>
     *
     * @param userId 用户ID
     * @param categoryId 分类ID
     * @return 试卷列表
     * @deprecated 请使用 findPapers(String, Long, String, Integer, Short) 方法
     */
    @Deprecated
    default List<PaperVO> findPapers(String userId, Long categoryId) {
        return findPapers(userId, categoryId, null, null, null);
    }

    /**
     * 逻辑删除
     *
     * @param id
     */
    Result<UserPaper> deleteById(String id);

    /**
     * 逻辑批量删除
     *
     * @param ids
     */
    Result<UserPaper> deleteBatch(List<String> ids);

    /**
     * 用户试卷关系更新状态
     */
    Result<UserPaper> updateStatusBatch(List<String> ids, Integer stauts);

    /**
     * 处理告警
     *
     * @param now
     */
    void processWarnStatus(String now);

    /**
     * 处理超时过期
     *
     * @param now
     */
    void processExpiredStatus(String now);

    /**
     * 找出没有推送的基础数据，
     * 只返回userId,paperId,time_factor两个字段
     *
     * @return
     */
    List<UserPaper> findNotPushCombination();

    List<UserPaper> findUserPush(List<String> ids);

    List<UserPaper> findPaperPush(Long paperId);

    /**
     * 后台页面列表
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<UserPaperVO> listPages(Page<UserPaperVO> page, QueryWrapper<UserPaperVO> queryWrapper);

    /**
     * 查询未考试的告警列表
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<TraineeVO> listExamWarnList(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper);

    /**
     * 用户试卷关系添加
     *
     * @param vo
     */
    void add(UserPaperVO vo);

    /**
     * 用户试卷关系更新状态
     *
     * @param vo
     */
    void updateStatus(UserPaperVO vo);

    void reset(Long id);

    void resetBatch(List<Long> idList);

    void fixPaperPushStatus(Long paperId);

    UserPaper getTop(LambdaQueryWrapper<UserPaper> queryWrapper);

    List<PaperVO> findPaperList(String userId);
}
