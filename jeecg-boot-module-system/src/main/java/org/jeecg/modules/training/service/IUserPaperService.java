package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
public interface IUserPaperService extends IService<UserPaper> {

    /**
     * 获取试卷
     *
     * @param userId
     * @param categoryId
     * @return
     */
    List<PaperVO> findPapers(String userId, Long categoryId);

    /**
     * 逻辑删除
     *
     * @param id
     */
    Result<UserPaper> deleteById(String id);

    /**
     * 逻辑批量删除
     *
     * @param ids
     */
    Result<UserPaper> deleteBatch(List<String> ids);

    /**
     * 用户试卷关系更新状态
     */
    Result<UserPaper> updateStatusBatch(List<String> ids, Integer stauts);

    /**
     * 处理告警
     *
     * @param now
     */
    void processWarnStatus(String now);

    /**
     * 处理超时过期
     *
     * @param now
     */
    void processExpiredStatus(String now);

    /**
     * 找出没有推送的基础数据，
     * 只返回userId,paperId,time_factor两个字段
     *
     * @return
     */
    List<UserPaper> findNotPushCombination();

    List<UserPaper> findUserPush(List<String> ids);

    List<UserPaper> findPaperPush(Long paperId);

    /**
     * 后台页面列表
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<UserPaperVO> listPages(Page<UserPaperVO> page, QueryWrapper<UserPaperVO> queryWrapper);

    /**
     * 查询未考试的告警列表
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<TraineeVO> listExamWarnList(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper);

    /**
     * 用户试卷关系添加
     *
     * @param vo
     */
    void add(UserPaperVO vo);

    /**
     * 用户试卷关系更新状态
     *
     * @param vo
     */
    void updateStatus(UserPaperVO vo);

    void reset(Long id);

    void resetBatch(List<Long> idList);

    void fixPaperPushStatus(Long paperId);

    UserPaper getTop(LambdaQueryWrapper<UserPaper> queryWrapper);

    List<PaperVO> findPaperList(String userId);
}
