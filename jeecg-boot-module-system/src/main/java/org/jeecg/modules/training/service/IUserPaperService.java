package org.jeecg.modules.training.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.training.entity.UserPaper;
import org.jeecg.modules.training.vo.PaperVO;
import org.jeecg.modules.training.vo.TraineeVO;
import org.jeecg.modules.training.vo.UserPaperVO;

import java.util.List;

/**
 * @Description: 试卷库
 * @Author: hzk
 * @Date: 2022-06-15
 * @Version: V1.0
 */
public interface IUserPaperService extends IService<UserPaper> {

    /**
     * 获取试卷（基础版本）
     *
     * <p>
     * 此方法提供基础的试卷查询功能，不包含会员权限过滤和自定义排序。
     * 适用于不需要会员制功能的场景或向后兼容的调用。
     * </p>
     *
     * @param userId 用户ID
     * @param categoryId 分类ID
     * @return 试卷列表
     */
    List<PaperVO> findPapers(String userId, Long categoryId);

    /**
     * APP端查询试卷列表（增强版本 - 带权限过滤和排序）
     *
     * <p>
     * 功能增强：
     *   根据 iteams_display_order 表进行自定义排序
     *   基于会员等级进行权限过滤
     *   支持企业会员制和船员会员制两种模式
     *   在SQL层面实现权限检查，提升查询效率
     * 将 findPapers 方法拆分为两个重载版本的原因:
     *   功能区分明确：基础版本用于简单查询，增强版本用于会员制场景
     *   参数一致性：增强版本的三个关键参数（departId、memberType、memberLevel）, 必须同时为有效值才能正确工作，分离接口可避免参数传递错误
     *   代码清晰：调用者可根据业务需求选择合适的方法
     * </p>
     *
     * @param userId 用户ID（船员ID）
     * @param categoryId 分类ID
     * @param departId 部门ID（必须有效）
     * @param memberType 会员类型（必须有效：0=船员会员模式, 1=企业会员模式）
     * @param memberLevel 会员等级（必须有效：0=无有效套餐, 1=基础版, 2=标准版, 3=尊享版）
     * @return 试卷列表（包含权限过滤后的试卷，按自定义顺序排列）
     */
    List<PaperVO> findPapers(String userId, Long categoryId, String departId, Integer memberType, Short memberLevel);

    /**
     * 逻辑删除
     *
     * @param id
     */
    Result<UserPaper> deleteById(String id);

    /**
     * 逻辑批量删除
     *
     * @param ids
     */
    Result<UserPaper> deleteBatch(List<String> ids);

    /**
     * 用户试卷关系更新状态
     */
    Result<UserPaper> updateStatusBatch(List<String> ids, Integer stauts);

    /**
     * 处理告警
     *
     * @param now
     */
    void processWarnStatus(String now);

    /**
     * 处理超时过期
     *
     * @param now
     */
    void processExpiredStatus(String now);

    /**
     * 找出没有推送的基础数据，
     * 只返回userId,paperId,time_factor两个字段
     *
     * @return
     */
    List<UserPaper> findNotPushCombination();

    List<UserPaper> findUserPush(List<String> ids);

    List<UserPaper> findPaperPush(Long paperId);

    /**
     * 后台页面列表
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<UserPaperVO> listPages(Page<UserPaperVO> page, QueryWrapper<UserPaperVO> queryWrapper);

    /**
     * 查询未考试的告警列表
     *
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<TraineeVO> listExamWarnList(Page<TraineeVO> page, QueryWrapper<TraineeVO> queryWrapper);

    /**
     * 用户试卷关系添加
     *
     * @param vo
     */
    void add(UserPaperVO vo);

    /**
     * 用户试卷关系更新状态
     *
     * @param vo
     */
    void updateStatus(UserPaperVO vo);

    void reset(Long id);

    void resetBatch(List<Long> idList);

    void fixPaperPushStatus(Long paperId);

    UserPaper getTop(LambdaQueryWrapper<UserPaper> queryWrapper);

    List<PaperVO> findPaperList(String userId);
}
