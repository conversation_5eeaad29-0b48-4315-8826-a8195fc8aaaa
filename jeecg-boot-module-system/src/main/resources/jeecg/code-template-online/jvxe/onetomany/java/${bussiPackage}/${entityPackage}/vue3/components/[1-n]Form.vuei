<#list subTables as sub>
<#if sub.foreignRelationType=='1'>
#segment#${sub.entityName}Form.vue
<template>
    <BasicForm @register="registerForm"/>
</template>
<script lang="ts">
    import {defineComponent} from 'vue';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {${sub.entityName?uncap_first}FormSchema} from '../${entityName?uncap_first}.data';
    import {defHttp} from '/@/utils/http/axios';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'

    export default defineComponent({
        name:"${sub.entityName}Form",
        components: {BasicForm},
        emits:['register'],
        setup(_,{emit}) {
            const [registerForm, {resetFields, setFieldsValue,getFieldsValue,validate}] = useForm({
                labelWidth: 150,
                schemas: ${sub.entityName?uncap_first}FormSchema,
                showActionButtonGroup: false,
            });
            /**
            *初始化加载数据
            */
            function initFormData(url,id){
                if(id){
                     defHttp.get({url,params:{id}},{isTransformResponse:false}).then(res=>{
                       res.success && setFieldsValue({...res.result[0]});
                    })
                }
            }
           /**
            *获取表单数据
            */
            function getFormData(){
               return [getFieldsValue()];
            }
            /**
            *表单校验
            */
            function validateForm(index){
                return new Promise((resolve, reject) => {
                    // 验证子表表单
                    validate().then(()=>{
                        return resolve()
                    }).catch(()=> {
                        return reject({ error: VALIDATE_FAILED ,index})
                    })
                })
            }
            return {
                registerForm,
                resetFields,
                initFormData,
                getFormData,
                validateForm
            }
        }
    })
</script>
</#if>
</#list>
