package org.jeecg.modules.training.async.report.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Comparator;

import javax.imageio.ImageIO;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.jeecg.modules.training.async.report.model.DetailExamBaseVO;
import org.jeecg.modules.training.async.report.model.DetailExamStandardVO;
import org.jeecg.modules.training.async.report.service.IFieldValueFormatter;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import static org.mockito.Mockito.mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

@RunWith(MockitoJUnitRunner.class)
public class StandardGeneratorTest {
    
    private static final String TEST_OUTPUT_DIR = "target/test-output/StandardGeneratorTest";
    private static final String TEST_DATA_DIR = "examReport-test-data";
    
    @Autowired
    private StandardGenerator standardGenerator;
    
    @InjectMocks
    private IFieldValueFormatter fieldValueFormatter = mock(IFieldValueFormatter.class);
    
    private static DetailExamStandardVO standardExamDetail;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    static {
        // 配置ObjectMapper
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        objectMapper.registerModule(new JavaTimeModule());
        
        // 注册BufferedImage反序列化器
        SimpleModule imageModule = new SimpleModule();
        imageModule.addDeserializer(BufferedImage.class, new BufferedImageDeserializer());
        objectMapper.registerModule(imageModule);
    }
    
    @BeforeClass
    public static void setUpClass() throws IOException {
        // 创建测试输出目录
        Path outputDir = Paths.get(TEST_OUTPUT_DIR);
        if (Files.exists(outputDir)) {
            Files.walk(outputDir)
                .sorted(Comparator.reverseOrder())
                .map(Path::toFile)
                .forEach(File::delete);
        }
        Files.createDirectories(outputDir);
        
        // 加载测试数据
        try (InputStream standardExamStream = StandardGeneratorTest.class.getClassLoader()
                .getResourceAsStream(TEST_DATA_DIR + "/standard-exam.json")) {
            
            assertNotNull("标准考试测试数据不存在", standardExamStream);
            standardExamDetail = objectMapper.readValue(standardExamStream, DetailExamStandardVO.class);
        }
    }
    
    @Before
    public void setUp() {
        ReflectionTestUtils.setField(standardGenerator, "FIELD_VALUE_FORMATTER", fieldValueFormatter);
        ReflectionTestUtils.setField(standardGenerator, "dataRoot", "src/test/resources");
    }
    
    @Test
    public void testGenerateReport() {
        // 准备测试数据
        String outputPath = TEST_OUTPUT_DIR + "/standard_exam_report.pdf";
        String templateName = "Standard-V1-0";
        
        // 执行测试
        standardGenerator.generateReport(templateName, standardExamDetail, outputPath);
        
        // 验证生成的PDF文件
        File outputFile = new File(outputPath);
        assertTrue("PDF文件应该被创建", outputFile.exists());
        assertTrue("PDF文件大小应该大于0", outputFile.length() > 0);
        assertValidPdfFile(outputFile);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateReportWithInvalidDetail() {
        // 测试使用非DetailStandardVO类型的数据
        DetailExamBaseVO invalidDetail = mock(DetailExamBaseVO.class);
        String templateName = "Standard-V1-0";
        standardGenerator.generateReport(templateName, invalidDetail, "invalid_output.pdf");
    }
    
    @Test(expected = RuntimeException.class)
    public void testGenerateReportWithInvalidPath() {
        // 测试使用无效的输出路径
        String templateName = "Standard-V1-0";
        standardGenerator.generateReport(templateName, standardExamDetail, "");
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateReportWithNullDetail() {
        // 测试使用null作为考试详情
        String templateName = "Standard-V1-0";
        standardGenerator.generateReport(templateName, null, TEST_OUTPUT_DIR + "/null_detail.pdf");
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testGenerateReportWithNullPath() {
        // 测试使用null作为输出路径
        String templateName = "Standard-V1-0";
        standardGenerator.generateReport(templateName, standardExamDetail, null);
    }
    
    /**
     * 验证生成的PDF文件是否有效
     */
    private void assertValidPdfFile(File pdfFile) {
        try {
            try (PDDocument document = PDDocument.load(pdfFile)) {
                assertNotNull("PDF文档不应为空", document);
                assertTrue("PDF页数应该大于0", document.getNumberOfPages() > 0);
            }
        } catch (IOException e) {
            fail("无法读取生成的PDF文件: " + e.getMessage());
        }
    }
    
    /**
     * BufferedImage反序列化器
     */
    public static class BufferedImageDeserializer extends JsonDeserializer<BufferedImage> {
        @Override
        public BufferedImage deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String imagePath = p.getValueAsString();
            
            // 从文件系统加载
            File imageFile = new File(imagePath);
            if (imageFile.exists() && imageFile.canRead()) {
                BufferedImage image = ImageIO.read(imageFile);
                if (image != null) {
                    return image;
                }
            }
            
            throw new IOException("无法加载图片文件: " + imagePath + 
                "。请确保图片文件存在于类路径或文件系统中。");
        }
    }
}
