# 修改sys_position表中code字段的UNIQUE索引为普通类型
ALTER TABLE sys_position
    DROP INDEX uniq_code;

# 向表sys_position中的code字段添加普通索引
ALTER TABLE sys_position
    ADD INDEX sys_position_code_index (code);

# 向表sys_position中添加字段del_flag，数据类型为tinyint(1)，默认值为0
ALTER TABLE sys_position
    ADD COLUMN del_flag TINYINT(1) DEFAULT 0 COMMENT '删除状态（0，正常，1已删除）';

# 向表phase中添加新字段icon，数据类型为varchar(255)
ALTER TABLE phase
    ADD COLUMN icon VARCHAR(255) COMMENT '图标';

# 向表sys_permission中添加新字段dep_route，数据类型为varchar(255)
ALTER TABLE sys_permission
    ADD COLUMN dep_route VARCHAR(255) COMMENT '过滤路径';

# 设置初始值
update sys_permission
set sys_permission.dep_route = '/79b24dc8981b4c159d6ab1bf166741c0'

-- run ok