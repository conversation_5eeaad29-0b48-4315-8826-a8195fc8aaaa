-- 删除原有会员价格字段
alter table sys_depart
    drop column member_amount;

-- 新建套餐定价ID字段
alter table sys_depart
    add package_price_id int default 1 not null comment '套餐定价ID';
alter table sys_depart
    add packages_version_type smallint default 3 not null comment '企业付费制租户的套餐等级, 1:基础版, 2:标准版, 3:尊享版, (member_type=1时有效)';

-- 创建套餐定价表
create table package_price
(
    id         int auto_increment comment '平台套餐价格ID固定为1'
        primary key,
    lev1_price decimal(10, 2) default 100.00 not null comment '基础版定价',
    lev2_price decimal(10, 2) default 200.00 not null comment '标准版定价',
    lev3_price decimal(10, 2) default 300.00 not null comment '尊享版定价',
    constraint package_lev1_price
        check (`lev1_price` > 0.00),
    constraint package_lev2_price
        check (`lev2_price` > 0.00),
    constraint package_lev3_price
        check (`lev3_price` > 0.00)
)
    comment '套餐定价';

-- 添加平台套餐价格记录(id=1)
insert into package_price () values ();

-- `packages`表中新增套餐类型字段
alter table packages
    add version_type smallint default 3 not null comment '套餐类型, 1:基础版, 2:标准版, 3:尊享版';

-- `order`表中新增商品相关字段
alter table orders
    add product_description varchar(64) default '' not null comment '商品描述 ，不能超过64个字符';
alter table orders
    add product_categorie smallint default 0 not null comment '购买商品类型, 0:套餐升级, 1:套餐续费, 2:单独购买考试, 3:单独购买学习资料';
alter table orders
    add product_detail bigint default 3 not null comment 'productCategorie=0 or 1，存储套餐lev:( 1:基础版, 2:标准版, 3: 尊享版)；productCategorie=2 or 3，此处存储资料id:(paper_id or study_id)';
alter table orders
    add product_startDate date null comment '订购商品起效日(单独购买你的考试资料、学习资料无有效期限制，此字段设为null)';
alter table orders
    add product_expireDate date null comment '订购商品截止日(单独购买你的考试资料、学习资料无有效期限制，此字段设为null)';

-- 创建`trainee_ordered_iteams`表
create table trainee_ordered_iteams
(
    id             int auto_increment,
    trainee_id     varchar(32) not null comment '船员ID',
    categorie_type smallint    not null comment '资料类型, 2:paper, 3:study',
    iteam_id       bigint      not null comment '资料ID(暂时使用share_id)',
    constraint trainee_ordered_iteams_pk
        primary key (id)
)
    comment '船员会员制个人已订购学习资料表';

create unique index trainee_ordered_iteams_trainee_id_categorie_type_iteam_id_uindex
    on trainee_ordered_iteams (trainee_id, categorie_type, iteam_id);

-- 创建`depart_ordered_iteams`表
create table depart_ordered_iteams
(
    id             int auto_increment,
    depart_id     varchar(32) not null comment '部门ID',
    categorie_type smallint    not null comment '资料类型, 2:paper, 3:study',
    iteam_id       bigint      not null comment '资料ID(暂时使用share_id)',
    constraint depart_ordered_iteams_pk
        primary key (id)
)
    comment '企业会员制个人已订购学习资料表';

create unique index depart_ordered_iteams_depart_id_categorie_type_iteam_id_uindex
    on depart_ordered_iteams (depart_id, categorie_type, iteam_id);

-- `study`表中添加字段
alter table study
    modify charge_status tinyint(1) default 1 not null comment '是否需要开通套餐';
alter table study
    add charge_type smallint default 1 not null comment '套餐类型, 1:基础版, 2:标准版, 3:尊享版, 99:套餐之外的资料,只能单独购买 (charge_status=1时起效)';
alter table study
    add iteam_price decimal(10, 2) null comment '单独购买此学习资料的价格(单位:元), null表示此资料不能单独购买';
alter table study
    add constraint study_iteam_price
        check (iteam_price IS NULL OR iteam_price > 0.00);

-- `paper`表中添加字段
alter table paper
    modify allow_check_answer tinyint(1) default 0 null comment '是否允许回查答题 否:0 是:1' after report_template;
alter table paper
    modify charge_status tinyint(1) default 1 not null comment '是否需要开通套餐';
alter table paper
    add charge_type smallint default 1 not null comment '套餐类型, 1:基础版, 2:标准版, 3:尊享版, 99:套餐之外的资料,只能单独购买 (charge_status=1时起效)';
alter table paper
    add iteam_price decimal(10, 2) null comment '单独购买此学习资料的价格(单位:元), null表示此资料不能单独购买';
alter table paper
    add constraint paper_iteam_price
        check (iteam_price IS NULL OR iteam_price > 0.00);

-- 新建`iteams_display_order`表
create table iteams_display_order
(
    id                 int auto_increment
        primary key,
    member_package_lev smallint not null comment '船员套餐等级， 0: 无有效套餐, 1: 基础版, 2: 标准版, 3: 尊享版',
    iteam_package_lev  smallint not null comment '资料所需套餐等级, 0: 免费, 1: 基础版, 2: 标准版, 3: 尊享版, 99: 不属于套餐、需单独购买',
    sequence           smallint not null comment '资料在App中的展示顺序'
)
    comment '定义各等级会员在App中看到的资料展示顺序';

create index iteams_display_order_lev_lev_sequence_index
    on iteams_display_order (member_package_lev, iteam_package_lev, sequence);

-- 向`iteams_display_order`表中导入数据
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (1, 0, 0, 0);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (2, 0, 99, 1);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (3, 0, 1, 2);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (4, 0, 2, 3);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (5, 0, 3, 4);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (6, 1, 1, 0);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (7, 1, 99, 1);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (8, 1, 2, 2);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (9, 1, 3, 3);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (10, 1, 0, 4);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (11, 2, 2, 0);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (12, 2, 99, 1);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (13, 2, 3, 2);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (14, 2, 1, 3);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (15, 2, 0, 4);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (16, 3, 3, 0);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (17, 3, 99, 1);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (18, 3, 2, 2);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (19, 3, 1, 3);
INSERT INTO iteams_display_order (id, member_package_lev, iteam_package_lev, sequence) VALUES (20, 3, 0, 4);
